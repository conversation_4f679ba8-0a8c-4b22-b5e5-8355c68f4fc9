/**
 * @file hlw8012_usage_example.c
 * @brief HLW8012多路驱动使用示例
 * <AUTHOR>
 * @date 2024
 */

#include "bsp_hlw8012.h"

/**
 * @brief HLW8012驱动使用示例
 * 
 * 这个文件展示了如何使用优化后的HLW8012多路驱动
 */

// 示例：初始化所有通道
void example_init_all_channels(void)
{
    uint8_t result = hlw8012_drv.init_all(D_NORMAL_MODE);
    
    if (result == HLW8012_OK) {
        // 初始化成功
        printf("HLW8012 all channels initialized successfully\n");
    } else {
        // 初始化失败
        printf("HLW8012 initialization failed with error: %d\n", result);
    }
}

// 示例：初始化单个通道
void example_init_single_channel(uint8_t channel)
{
    uint8_t result = hlw8012_drv.init(channel, D_NORMAL_MODE);
    
    if (result == HLW8012_OK) {
        printf("Channel %d initialized successfully\n", channel);
    } else {
        printf("Channel %d initialization failed with error: %d\n", channel, result);
    }
}

// 示例：动态配置引脚
void example_configure_pin(uint8_t channel, GPIO_TypeDef *gpio, uint16_t pin)
{
    uint8_t result = hlw8012_drv.set_pin_config(channel, gpio, pin);
    
    if (result == HLW8012_OK) {
        printf("Channel %d pin configured successfully\n", channel);
    } else {
        printf("Channel %d pin configuration failed with error: %d\n", channel, result);
    }
}

// 示例：使能/禁用通道
void example_channel_control(void)
{
    // 禁用通道3
    uint8_t result = hlw8012_drv.disable_channel(3);
    if (result == HLW8012_OK) {
        printf("Channel 3 disabled\n");
    }
    
    // 使能通道5
    result = hlw8012_drv.enable_channel(5);
    if (result == HLW8012_OK) {
        printf("Channel 5 enabled\n");
    }
    
    // 检查通道状态
    uint8_t state = hlw8012_drv.get_channel_state(3);
    switch (state) {
        case HLW8012_CHANNEL_DISABLED:
            printf("Channel 3 is disabled\n");
            break;
        case HLW8012_CHANNEL_ENABLED:
            printf("Channel 3 is enabled\n");
            break;
        case HLW8012_CHANNEL_ERROR:
            printf("Channel 3 has error\n");
            break;
        default:
            printf("Channel 3 unknown state\n");
            break;
    }
}

// 示例：读取所有通道数据
void example_read_all_channels(void)
{
    printf("Reading all channel data:\n");
    printf("Channel | Power(W) | Energy(0.01kWh) | State\n");
    printf("--------|----------|-----------------|-------\n");
    
    for (uint8_t i = 0; i < TOTAL_CHARGE_PORT; i++) {
        // 检查通道是否有效
        if (!bsp_hlw8012_is_channel_valid(i)) {
            continue;
        }
        
        uint8_t state = hlw8012_drv.get_channel_state(i);
        if (state == HLW8012_CHANNEL_ENABLED) {
            uint16_t power = bsp_hlw8012_get_channel_power(i);
            uint16_t energy = bsp_hlw8012_get_channel_energy(i);
            
            printf("   %2d   |  %4d.%dW |      %4d       | EN\n", 
                   i, power/10, power%10, energy);
        } else {
            printf("   %2d   |    --    |       --        | DIS\n", i);
        }
    }
}

// 示例：校准指定通道
void example_calibrate_channel(uint8_t channel)
{
    if (!bsp_hlw8012_is_channel_valid(channel)) {
        printf("Invalid channel: %d\n", channel);
        return;
    }
    
    uint8_t state = hlw8012_drv.get_channel_state(channel);
    if (state != HLW8012_CHANNEL_ENABLED) {
        printf("Channel %d is not enabled\n", channel);
        return;
    }
    
    printf("Starting calibration for channel %d...\n", channel);
    bsp_hlw8012_start_calibration(channel);
    
    // 校准过程需要等待36秒（D_TIME1_CAL_TIME）
    printf("Calibration started. Please wait 36 seconds...\n");
}

// 示例：重置通道能量计数
void example_reset_energy(uint8_t channel)
{
    if (!bsp_hlw8012_is_channel_valid(channel)) {
        printf("Invalid channel: %d\n", channel);
        return;
    }
    
    bsp_hlw8012_reset_channel_energy(channel);
    printf("Channel %d energy counter reset\n", channel);
}

// 示例：定时任务 - 在主循环或定时器中调用
void example_periodic_task(void)
{
    // 扫描CF引脚状态变化（如果不使用硬件中断）
    scan_cf_pins();
    
    // 获取功率数据
    hlw8012_drv.get_power();
    
    // 可以在这里添加其他周期性任务
}

// 示例：中断处理 - 在中断服务程序中调用
void example_interrupt_handlers(void)
{
    // 在定时器中断中调用（通常1ms定时器）
    // hlw8012_drv.timer_callback();
    
    // 在外部中断中调用（GPIO中断）
    // hlw8012_drv.exti_callback(GPIO_Pin_10); // 示例引脚
}

// 示例：完整的初始化和使用流程
void example_complete_usage(void)
{
    printf("=== HLW8012 Multi-Channel Driver Example ===\n");
    
    // 1. 初始化所有通道
    example_init_all_channels();
    
    // 2. 配置特定通道的引脚（如果需要）
    example_configure_pin(0, GPIOB, GPIO_Pin_10);
    
    // 3. 控制通道使能状态
    example_channel_control();
    
    // 4. 读取所有通道数据
    example_read_all_channels();
    
    // 5. 校准通道（如果需要）
    // example_calibrate_channel(0);
    
    // 6. 重置能量计数（如果需要）
    // example_reset_energy(0);
    
    printf("=== Example completed ===\n");
}

/**
 * @brief 错误处理示例
 */
void example_error_handling(void)
{
    uint8_t result;
    
    // 尝试操作无效通道
    result = hlw8012_drv.init(255, D_NORMAL_MODE);
    if (result == HLW8012_ERROR_INVALID_CH) {
        printf("Error: Invalid channel detected\n");
    }
    
    // 尝试设置无效引脚
    result = hlw8012_drv.set_pin_config(0, NULL, GPIO_Pin_0);
    if (result == HLW8012_ERROR_PARAM) {
        printf("Error: Invalid parameter detected\n");
    }
}

/**
 * @brief 性能优化示例
 */
void example_performance_optimization(void)
{
    // 只使能需要的通道，禁用不用的通道以节省CPU资源
    for (uint8_t i = 0; i < TOTAL_CHARGE_PORT; i++) {
        if (i < 5) {
            // 只使能前5个通道
            hlw8012_drv.enable_channel(i);
        } else {
            // 禁用其他通道
            hlw8012_drv.disable_channel(i);
        }
    }
    
    printf("Performance optimization: Only channels 0-4 are enabled\n");
}
