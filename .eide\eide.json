{"name": "ChargeStation", "type": "ARM", "dependenceList": [], "srcDirs": ["FreeRTOS", "App", "Bsp", "<PERSON><PERSON><PERSON>", "User", "Components"], "virtualFolder": {"name": "<virtual_root>", "files": [], "folders": []}, "outDir": "build", "deviceName": "HK32F030R8Tx", "packDir": ".pack/HKMicroChip/HK32F0xx_DFP.1.0.8", "miscInfo": {"uid": "53a93f6b5d2653cc876925f39c0c237c"}, "targets": {"Debug": {"excludeList": ["fwlib/CMSIS/CM0/DeviceSupport/startup/startup_hk32f030x6.s", "fwlib/CMSIS/CM0/DeviceSupport/startup/startup_hk32f031x6.s", "fwlib/CMSIS/CM0/DeviceSupport/startup/startup_hk32f04ax.s", "fwlib/CMSIS/CM0/DeviceSupport/startup/startup_hk32f0xx.s", "fwlib/CMSIS/CM0/CoreSupport/core_cm3.c", "Fwlib/CMSIS/CM0/CoreSupport/core_cm3.c", "Fwlib/CMSIS/CM0/DeviceSupport/startup/startup_hk32f030x6.s", "Fwlib/CMSIS/CM0/DeviceSupport/startup/startup_hk32f031x6.s", "Fwlib/CMSIS/CM0/DeviceSupport/startup/startup_hk32f04ax.s", "Fwlib/CMSIS/CM0/DeviceSupport/startup/startup_hk32f0xx.s", "FreeRTOS/portable/GCC/ARM7_AT91FR40008", "FreeRTOS/portable/GCC/ARM7_AT91SAM7S", "FreeRTOS/portable/GCC/ARM7_LPC2000", "FreeRTOS/portable/GCC/ARM7_LPC23xx", "FreeRTOS/portable/GCC/ARM_AARCH64", "FreeRTOS/portable/GCC/ARM_AARCH64_SRE", "FreeRTOS/portable/GCC/ARM_CA53_64_BIT", "FreeRTOS/portable/GCC/ARM_CA53_64_BIT_SRE", "FreeRTOS/portable/GCC/ARM_CA9", "FreeRTOS/portable/GCC/ARM_CM23", "FreeRTOS/portable/GCC/ARM_CM23_NTZ", "FreeRTOS/portable/GCC/ARM_CM3", "FreeRTOS/portable/GCC/ARM_CM33", "FreeRTOS/portable/GCC/ARM_CM33_NTZ", "FreeRTOS/portable/GCC/ARM_CM35P", "FreeRTOS/portable/GCC/ARM_CM35P_NTZ", "FreeRTOS/portable/GCC/ARM_CM3_MPU", "FreeRTOS/portable/GCC/ARM_CM4F", "FreeRTOS/portable/GCC/ARM_CM4_MPU", "FreeRTOS/portable/GCC/ARM_CM55", "FreeRTOS/portable/GCC/ARM_CM55_NTZ", "FreeRTOS/portable/GCC/ARM_CM7", "FreeRTOS/portable/GCC/ARM_CM85", "FreeRTOS/portable/GCC/ARM_CM85_NTZ", "FreeRTOS/portable/GCC/ARM_CR5", "FreeRTOS/portable/GCC/ARM_CRx_MPU", "FreeRTOS/portable/GCC/ARM_CRx_No_GIC", "FreeRTOS/portable/GCC/ATMega323", "FreeRTOS/portable/GCC/AVR32_UC3", "FreeRTOS/portable/GCC/AVR_AVRDx", "FreeRTOS/portable/GCC/AVR_Mega0", "FreeRTOS/portable/GCC/ColdFire_V2", "FreeRTOS/portable/GCC/CORTUS_APS3", "FreeRTOS/portable/GCC/H8S2329", "FreeRTOS/portable/GCC/HCS12", "FreeRTOS/portable/GCC/IA32_flat", "FreeRTOS/portable/GCC/MCF5235", "FreeRTOS/portable/GCC/MicroBlaze", "FreeRTOS/portable/GCC/MicroBlazeV8", "FreeRTOS/portable/GCC/MicroBlazeV9", "FreeRTOS/portable/GCC/MSP430F449", "FreeRTOS/portable/GCC/NiosII", "FreeRTOS/portable/GCC/PPC405_Xilinx", "FreeRTOS/portable/GCC/PPC440_Xilinx", "FreeRTOS/portable/GCC/RISC-V", "FreeRTOS/portable/GCC/RL78", "FreeRTOS/portable/GCC/RX100", "FreeRTOS/portable/GCC/RX200", "FreeRTOS/portable/GCC/RX600", "FreeRTOS/portable/GCC/RX600v2", "FreeRTOS/portable/GCC/RX700v3_DPFPU", "FreeRTOS/portable/GCC/STR75x", "FreeRTOS/portable/GCC/TriCore_1782", "FreeRTOS/portable/MemMang/heap_1.c", "FreeRTOS/portable/MemMang/heap_2.c", "FreeRTOS/portable/MemMang/heap_3.c", "FreeRTOS/portable/MemMang/heap_5.c", "FreeRTOS/portable/RVDS/ARM7_LPC21xx", "FreeRTOS/portable/RVDS/ARM_CA9", "FreeRTOS/portable/RVDS/ARM_CM3", "FreeRTOS/portable/RVDS/ARM_CM4F", "FreeRTOS/portable/RVDS/ARM_CM4_MPU", "FreeRTOS/portable/RVDS/ARM_CM7", "FreeRTOS/portable/GCC", "Components/ringbuffer", "Components/lwrb-3.2.0"], "toolchain": "AC5", "compileConfig": {"cpuType": "Cortex-M0", "floatingPointHardware": "none", "useCustomScatterFile": false, "scatterFilePath": "<YOUR_SCATTER_FILE>.sct", "storageLayout": {"RAM": [{"tag": "IRAM", "id": 1, "mem": {"startAddr": "0x20000000", "size": "0x2800"}, "isChecked": true, "noInit": false}], "ROM": [{"tag": "IROM", "id": 1, "mem": {"startAddr": "0x08000000", "size": "0x10000"}, "isChecked": true, "isStartup": true}]}, "options": "null"}, "uploader": "OpenOCD", "uploadConfig": {"bin": "", "target": "stm32f0x", "interface": "cmsis-dap-v1", "baseAddr": "0x08000000"}, "uploadConfigMap": {"JLink": {"bin": "", "baseAddr": "", "cpuInfo": {"vendor": "null", "cpuName": "null"}, "proType": 1, "speed": 8000, "otherCmds": ""}}, "custom_dep": {"name": "default", "incList": ["."], "libList": [], "defineList": ["USE_STDPERIPH_DRIVER", "HK32F030"]}, "builderOptions": {"GCC": {"version": 5, "beforeBuildTasks": [], "afterBuildTasks": [], "global": {"$float-abi-type": "softfp", "output-debug-info": "enable", "misc-control": "--specs=nosys.specs --specs=nano.specs"}, "c/cpp-compiler": {"language-c": "c11", "language-cpp": "c++11", "optimization": "level-debug", "warnings": "all-warnings", "one-elf-section-per-function": true, "one-elf-section-per-data": true}, "asm-compiler": {}, "linker": {"output-format": "elf", "remove-unused-input-sections": true, "LIB_FLAGS": "-lm", "$toolName": "auto"}}, "AC6": {"version": 3, "beforeBuildTasks": [], "afterBuildTasks": [], "global": {"use-microLIB": true, "output-debug-info": "enable"}, "c/cpp-compiler": {"optimization": "level-1", "language-c": "c99", "language-cpp": "c++11", "one-elf-section-per-function": true, "warnings": "ac5-like-warnings"}, "asm-compiler": {"$use": "asm-auto"}, "linker": {"output-format": "elf", "misc-controls": "--diag_suppress=L6329"}}, "AC5": {"version": 4, "beforeBuildTasks": [], "afterBuildTasks": [], "global": {"use-microLIB": true, "output-debug-info": "enable"}, "c/cpp-compiler": {"optimization": "level-1", "one-elf-section-per-function": true, "c99-mode": true, "C_FLAGS": "--diag_suppress=1 --diag_suppress=1295", "CXX_FLAGS": "--diag_suppress=1 --diag_suppress=1295", "warnings": "unspecified"}, "asm-compiler": {}, "linker": {"output-format": "elf"}}}}}, "version": "3.5"}