#ifndef __BSP_HLW8012_H__
#define __BSP_HLW8012_H__

#include "syscfg.h"

// 定义充电口总数，可以根据实际项目需求修改
#ifndef TOTAL_CHARGE_PORT
#define TOTAL_CHARGE_PORT 10 // 支持的最大充电口数量
#endif

// 通道状态定义
#define HLW8012_CHANNEL_DISABLED    0x00  // 通道禁用
#define HLW8012_CHANNEL_ENABLED     0x01  // 通道使能
#define HLW8012_CHANNEL_ERROR       0x02  // 通道错误

// 错误代码定义
#define HLW8012_OK                  0x00  // 操作成功
#define HLW8012_ERROR_INVALID_CH    0x01  // 无效通道
#define HLW8012_ERROR_NOT_INIT      0x02  // 未初始化
#define HLW8012_ERROR_PARAM         0x03  // 参数错误

// Time1定时器定时,时间基数 = 1ms
#define D_TIME1_20MS       20
#define D_TIME1_100MS      100
#define D_TIME1_150MS      150
#define D_TIME1_200MS      200
#define D_TIME1_400MS      400
#define D_TIME1_500MS      500
#define D_TIME1_1S         1000 // Time1定时器定时1S时间常数
#define D_TIME1_2S         2000
#define D_TIME1_3S         3000
#define D_TIME1_4S         4000
#define D_TIME1_6S         6000
#define D_TIME1_8S         8000
#define D_TIME1_9S         9000
#define D_TIME1_10S        10000
#define D_TIME1_20S        20000

#define D_TIME1_V_OVERFLOW 500  // Time1定时器,电压溢出常数设定为500mS,溢出说明脉宽周期大于500mS
#define D_TIME1_I_OVERFLOW 8000 // Time1定时器,电流溢出常数设定为10S,溢出说明脉宽周期大于10S
#define D_TIME1_P_OVERFLOW 5000 // Time1定时器,功率溢出常数设定为10S(约0.5W最小值),溢出说明脉宽周期大于10S
// #define D_TIME1_P_OVERFLOW 40000 //Time1定时器,功率溢出常数设定为40S(约0.2W最小值)
#define D_TIME1_CAL_TIME   36000 // 校正时间，记录在此时间内的脉冲数，1000W负载在用电36S时间内耗费0.01度电

// 工作模式
//--------------------------------------------------------------------------------------------
//--------------------------------------------------------------------------------------------
#define D_ERR_MODE         0x00 // 错误提示模式
#define D_NORMAL_MODE      0x10 // 正常工作模式
#define D_CAL_START_MODE   0x21 // 校正模式，启动
#define D_CAL_END_MODE     0x23 // 校正模式，完成

// 测量模式
#define D_POWER_MODE       0 // 功率测量模式
#define D_VOLTAGE_MODE     1 // 电压测量模式
#define D_CURRENT_MODE     2 // 电流测量模式

// 只保留功率相关结构体
typedef struct
{
    uint32_t U32_P_CURRENT_PLUSEWIDTH_TIME; // 当前功率 脉冲周期
    uint16_t U16_P_TotalTimes;              // 当前脉冲 功率测量总时间
    uint16_t U16_P_OneCycleTime;            // 功率测量时间参数
    uint16_t U16_P_Last_OneCycleTime;       // 功率测量时间参数，上一次数量值
    uint16_t U16_P_CNT;                     // 功率测量脉冲数量
    uint16_t U16_P_Last_CNT;                // 功率测量脉冲数量，上一次数量值
    uint16_t U16_AC_P;                      // 功率值 1000.0W.
    uint8_t B_P_TestOneCycle_Mode : 1;      // 功率测量模式 1:单周期测量，0:1S定时测量
    uint8_t B_P_Last_TestOneCycle_Mode : 1; // 上一次的功率测量模式
    uint8_t B_P_OVERFLOW : 1;               // 功率脉冲周期 溢出标志位
    uint8_t B_P_Last_OVERFLOW : 1;          // 上一次的功率脉冲周期 溢出标志位
    uint8_t reserved : 4;                   // 保留位
} t_power_data;

typedef struct
{
    uint32_t U32_P_REF_PLUSEWIDTH_TIME; // 参考功率 脉冲周期
    uint16_t U16_REF_001_E_Pluse_CNT;   // 0.01度电脉冲总数参考值
} t_refrence_data;

typedef struct
{
    uint16_t U16_AC_E;        // 用电量   0.01度
    uint16_t U16_E_Pluse_CNT; // 脉冲个数
} t_energy_data;

typedef struct
{
    uint16_t U16_Cal_Times; // 校正时间
} t_time_data;

typedef struct
{
    t_power_data power;
    t_energy_data energy;
    t_time_data time;
    t_refrence_data refrence;
    uint8_t WorkMode;
    uint8_t channel_state;      // 通道状态
    uint8_t error_code;         // 错误代码
    uint32_t last_update_time;  // 最后更新时间戳
} t_hlw8012_data;

// 引脚配置结构体
typedef struct {
    GPIO_TypeDef *gpio;
    uint16_t pin;
    uint8_t enabled;            // 引脚是否使能
} t_hlw8012_pin_config;

// HLW8012驱动接口，优化后的多路驱动接口
typedef struct
{
    uint8_t (*init)(uint8_t channel, uint8_t mode);           // 初始化函数
    uint8_t (*init_all)(uint8_t mode);                        // 初始化所有通道
    void (*exti_callback)(uint16_t GPIO_Pin);                 // 外部中断回调
    void (*timer_callback)(void);                             // 定时器回调
    void (*get_power)(void);                                  // 获取功率
    uint16_t (*get_energy)(uint8_t channel);                  // 获取能量
    uint8_t (*enable_channel)(uint8_t channel);               // 使能通道
    uint8_t (*disable_channel)(uint8_t channel);              // 禁用通道
    uint8_t (*get_channel_state)(uint8_t channel);            // 获取通道状态
    uint8_t (*set_pin_config)(uint8_t channel, GPIO_TypeDef *gpio, uint16_t pin); // 设置引脚配置
} t_hlw8012_drv;

// 导出变量声明
extern t_hlw8012_data hlw8012_detect_channel[TOTAL_CHARGE_PORT];
extern const t_hlw8012_drv hlw8012_drv;

// 函数原型声明
void init_calibration_data(void);
void scan_cf_pins(void);
uint8_t bsp_hlw8012_init(uint8_t channel, uint8_t mode);
uint8_t bsp_hlw8012_init_all(uint8_t mode);
uint8_t bsp_hlw8012_enable_channel(uint8_t channel);
uint8_t bsp_hlw8012_disable_channel(uint8_t channel);
uint8_t bsp_hlw8012_get_channel_state(uint8_t channel);
uint8_t bsp_hlw8012_set_pin_config(uint8_t channel, GPIO_TypeDef *gpio, uint16_t pin);
void bsp_hlw8012_start_calibration(uint8_t channel);
void bsp_hlw8012_save_calibration_data(void);
void bsp_hlw8012_load_calibration_data(void);
void bsp_hlw8012_read_all_channels(void);
uint16_t bsp_hlw8012_get_channel_power(uint8_t channel);
uint16_t bsp_hlw8012_get_channel_energy(uint8_t channel);
void bsp_hlw8012_reset_channel_energy(uint8_t channel);
uint8_t bsp_hlw8012_is_channel_valid(uint8_t channel);

#endif
