#ifndef __BSP_HLW8012_H__
#define __BSP_HLW8012_H__

#include "syscfg.h"

// 定义充电口总数，可以根据实际项目需求修改
#ifndef TOTAL_CHARGE_PORT
#define TOTAL_CHARGE_PORT 10 // 支持的最大充电口数量
#endif

// Time1定时器定时,时间基数 = 1ms
#define D_TIME1_20MS       20
#define D_TIME1_100MS      100
#define D_TIME1_150MS      150
#define D_TIME1_200MS      200
#define D_TIME1_400MS      400
#define D_TIME1_500MS      500
#define D_TIME1_1S         1000 // Time1定时器定时1S时间常数
#define D_TIME1_2S         2000
#define D_TIME1_3S         3000
#define D_TIME1_4S         4000
#define D_TIME1_6S         6000
#define D_TIME1_8S         8000
#define D_TIME1_9S         9000
#define D_TIME1_10S        10000
#define D_TIME1_20S        20000

#define D_TIME1_V_OVERFLOW 500  // Time1定时器,电压溢出常数设定为500mS,溢出说明脉宽周期大于500mS
#define D_TIME1_I_OVERFLOW 8000 // Time1定时器,电流溢出常数设定为10S,溢出说明脉宽周期大于10S
#define D_TIME1_P_OVERFLOW 5000 // Time1定时器,功率溢出常数设定为10S(约0.5W最小值),溢出说明脉宽周期大于10S
// #define D_TIME1_P_OVERFLOW 40000 //Time1定时器,功率溢出常数设定为40S(约0.2W最小值)
#define D_TIME1_CAL_TIME   36000 // 校正时间，记录在此时间内的脉冲数，1000W负载在用电36S时间内耗费0.01度电

// 工作模式
//--------------------------------------------------------------------------------------------
//--------------------------------------------------------------------------------------------
#define D_ERR_MODE         0x00 // 错误提示模式
#define D_NORMAL_MODE      0x10 // 正常工作模式
#define D_CAL_START_MODE   0x21 // 校正模式，启动
#define D_CAL_END_MODE     0x23 // 校正模式，完成

// 测量模式
#define D_POWER_MODE       0 // 功率测量模式
#define D_VOLTAGE_MODE     1 // 电压测量模式
#define D_CURRENT_MODE     2 // 电流测量模式

// 简化的功率数据结构
typedef struct
{
    uint16_t U16_P_OneCycleTime;            // 时间计数（ms）
    uint16_t U16_P_CNT;                     // 脉冲计数
    uint16_t U16_AC_P;                      // 功率值（0.1W）
} t_power_data;

// 简化的能量数据结构
typedef struct
{
    uint16_t U16_AC_E;        // 用电量（0.01度）
    uint16_t U16_E_Pluse_CNT; // 脉冲计数
} t_energy_data;

// 简化的通道数据结构
typedef struct
{
    t_power_data power;   // 功率数据
    t_energy_data energy; // 能量数据
} t_hlw8012_data;

// HLW8012驱动接口，简化版多路驱动
typedef struct
{
    void (*init)(void);                      // 初始化所有通道
    void (*exti_callback)(uint16_t GPIO_Pin); // 外部中断回调
    void (*timer_callback)(void);            // 定时器回调
    uint16_t (*get_power)(uint8_t channel);  // 获取指定通道功率
    uint16_t (*get_energy)(uint8_t channel); // 获取指定通道能量
} t_hlw8012_drv;

// 导出变量声明
extern t_hlw8012_data hlw8012_detect_channel[TOTAL_CHARGE_PORT];
extern const t_hlw8012_drv hlw8012_drv;

// 简化的函数原型声明
void bsp_hlw8012_init(void);                          // 初始化所有通道
uint16_t bsp_hlw8012_get_power(uint8_t channel);      // 获取通道功率
uint16_t bsp_hlw8012_get_energy(uint8_t channel);     // 获取通道能量
void bsp_hlw8012_scan_pins(void);                     // 扫描引脚状态（轮询模式）

#endif
