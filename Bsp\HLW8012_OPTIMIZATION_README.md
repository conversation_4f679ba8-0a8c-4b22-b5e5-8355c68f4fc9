# HLW8012多路驱动优化说明

## 优化概述

本次优化针对HLW8012多路驱动进行了全面改进，提升了代码的可维护性、可靠性和性能。

## 主要优化内容

### 1. 增强的错误处理机制

- **新增错误代码定义**：
  - `HLW8012_OK`: 操作成功
  - `HLW8012_ERROR_INVALID_CH`: 无效通道
  - `HLW8012_ERROR_NOT_INIT`: 未初始化
  - `HLW8012_ERROR_PARAM`: 参数错误

- **通道有效性检查**：所有函数都会检查通道号的有效性
- **返回值优化**：关键函数返回错误代码而不是void

### 2. 通道状态管理

- **新增通道状态**：
  - `HLW8012_CHANNEL_DISABLED`: 通道禁用
  - `HLW8012_CHANNEL_ENABLED`: 通道使能
  - `HLW8012_CHANNEL_ERROR`: 通道错误

- **动态通道控制**：
  - `bsp_hlw8012_enable_channel()`: 使能指定通道
  - `bsp_hlw8012_disable_channel()`: 禁用指定通道
  - `bsp_hlw8012_get_channel_state()`: 获取通道状态

### 3. 灵活的引脚配置

- **动态引脚配置**：支持运行时修改引脚配置
- **引脚使能控制**：每个引脚可以独立使能/禁用
- **引脚配置函数**：`bsp_hlw8012_set_pin_config()`

### 4. 性能优化

- **条件处理**：只处理使能的通道，减少不必要的计算
- **优化扫描**：`scan_cf_pins()`只扫描使能的引脚
- **中断优化**：移除了重复的中断处理调用

### 5. 新增实用接口

```c
// 通道管理
uint8_t bsp_hlw8012_init_all(uint8_t mode);
uint8_t bsp_hlw8012_enable_channel(uint8_t channel);
uint8_t bsp_hlw8012_disable_channel(uint8_t channel);
uint8_t bsp_hlw8012_get_channel_state(uint8_t channel);

// 数据获取
uint16_t bsp_hlw8012_get_channel_power(uint8_t channel);
uint16_t bsp_hlw8012_get_channel_energy(uint8_t channel);
void bsp_hlw8012_reset_channel_energy(uint8_t channel);

// 配置管理
uint8_t bsp_hlw8012_set_pin_config(uint8_t channel, GPIO_TypeDef *gpio, uint16_t pin);
uint8_t bsp_hlw8012_is_channel_valid(uint8_t channel);
```

### 6. 数据结构增强

```c
typedef struct
{
    t_power_data power;
    t_energy_data energy;
    t_time_data time;
    t_refrence_data refrence;
    uint8_t WorkMode;
    uint8_t channel_state;      // 新增：通道状态
    uint8_t error_code;         // 新增：错误代码
    uint32_t last_update_time;  // 新增：最后更新时间戳
} t_hlw8012_data;

typedef struct {
    GPIO_TypeDef *gpio;
    uint16_t pin;
    uint8_t enabled;            // 新增：引脚使能标志
} t_hlw8012_pin_config;
```

## 使用方法

### 基本初始化

```c
// 方法1：初始化所有通道
uint8_t result = hlw8012_drv.init_all(D_NORMAL_MODE);
if (result != HLW8012_OK) {
    // 处理错误
}

// 方法2：初始化单个通道
result = hlw8012_drv.init(0, D_NORMAL_MODE);
if (result != HLW8012_OK) {
    // 处理错误
}
```

### 通道控制

```c
// 禁用不需要的通道
hlw8012_drv.disable_channel(3);

// 使能需要的通道
hlw8012_drv.enable_channel(5);

// 检查通道状态
uint8_t state = hlw8012_drv.get_channel_state(0);
```

### 数据读取

```c
// 读取功率
uint16_t power = bsp_hlw8012_get_channel_power(0);

// 读取能量
uint16_t energy = bsp_hlw8012_get_channel_energy(0);

// 重置能量计数
bsp_hlw8012_reset_channel_energy(0);
```

### 动态配置

```c
// 配置引脚
hlw8012_drv.set_pin_config(0, GPIOB, GPIO_Pin_10);
```

## 兼容性

- **向后兼容**：保留了原有的接口函数
- **渐进升级**：可以逐步迁移到新接口
- **配置兼容**：默认配置与原版本相同

## 性能提升

1. **CPU使用率降低**：只处理使能的通道
2. **内存使用优化**：增加了状态管理但总体内存使用合理
3. **响应速度提升**：减少了不必要的计算和检查
4. **错误恢复能力**：增强的错误处理机制

## 注意事项

1. **时间戳功能**：`get_system_tick()`需要根据实际系统实现
2. **中断配置**：确保GPIO中断正确配置
3. **定时器配置**：确保1ms定时器正确调用`timer_callback`
4. **校准数据**：需要实现EEPROM/Flash的读写函数

## 测试建议

1. **功能测试**：测试所有新增接口的功能
2. **性能测试**：对比优化前后的CPU使用率
3. **稳定性测试**：长时间运行测试
4. **错误处理测试**：测试各种错误情况的处理

## 示例代码

详细的使用示例请参考 `hlw8012_usage_example.c` 文件。

## 总结

本次优化显著提升了HLW8012多路驱动的：
- **可靠性**：完善的错误处理和状态管理
- **灵活性**：动态配置和通道控制
- **性能**：优化的处理逻辑和资源使用
- **可维护性**：清晰的代码结构和接口设计

优化后的驱动更适合在实际项目中使用，特别是需要管理多个充电口的充电桩应用。
