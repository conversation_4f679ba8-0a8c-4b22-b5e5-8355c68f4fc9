#include "bsp.h"

extern void xPortSysTickHand<PERSON>(void);

/**
 * @brief 初始化
 */
void bsp_init(void)
{
    /* 初始化系统时钟 */
    bsp_clock_init();

    /* 初始化24C08 EEPROM */
    BSP_EE_Init();

    /* 初始化蜂鸣器 */
    bsp_buzzer_init();

    /* 初始化继电器 */
    bsp_relay_init();

    /* 初始化HLW8012 */
    bsp_hlw8012_init();

    /* 初始化SPI */
    bsp_spi_init();

    /* 初始化UART */
    bsp_usart_init();

    /* 初始化TM1640 */
    bsp_tm1640_init();
}

/**
 * @brief 系统滴答中断处理函数
 */
void SysTick_Handler(void)
{
    if (xTaskGetSchedulerState() != taskSCHEDULER_NOT_STARTED) {
        xPortSysTickHandler();
    }
}
