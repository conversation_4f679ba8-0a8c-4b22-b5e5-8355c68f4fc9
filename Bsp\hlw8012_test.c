/**
 * @file hlw8012_test.c
 * @brief HLW8012多路驱动测试程序
 * <AUTHOR>
 * @date 2024
 */

#include "bsp_hlw8012.h"
#include <stdio.h>

// 测试结果统计
static uint32_t test_passed = 0;
static uint32_t test_failed = 0;

// 测试宏定义
#define TEST_ASSERT(condition, test_name) \
    do { \
        if (condition) { \
            printf("[PASS] %s\n", test_name); \
            test_passed++; \
        } else { \
            printf("[FAIL] %s\n", test_name); \
            test_failed++; \
        } \
    } while(0)

/**
 * @brief 测试通道有效性检查
 */
void test_channel_validity(void)
{
    printf("\n=== Testing Channel Validity ===\n");
    
    // 测试有效通道
    TEST_ASSERT(bsp_hlw8012_is_channel_valid(0) == 1, "Valid channel 0");
    TEST_ASSERT(bsp_hlw8012_is_channel_valid(TOTAL_CHARGE_PORT-1) == 1, "Valid max channel");
    
    // 测试无效通道
    TEST_ASSERT(bsp_hlw8012_is_channel_valid(TOTAL_CHARGE_PORT) == 0, "Invalid channel (max+1)");
    TEST_ASSERT(bsp_hlw8012_is_channel_valid(255) == 0, "Invalid channel 255");
}

/**
 * @brief 测试驱动初始化
 */
void test_driver_initialization(void)
{
    printf("\n=== Testing Driver Initialization ===\n");
    
    // 测试单通道初始化
    uint8_t result = bsp_hlw8012_init(0, D_NORMAL_MODE);
    TEST_ASSERT(result == HLW8012_OK, "Single channel initialization");
    
    // 测试无效通道初始化
    result = bsp_hlw8012_init(255, D_NORMAL_MODE);
    TEST_ASSERT(result == HLW8012_ERROR_INVALID_CH, "Invalid channel initialization");
    
    // 测试所有通道初始化
    result = hlw8012_drv.init_all(D_NORMAL_MODE);
    TEST_ASSERT(result == HLW8012_OK, "All channels initialization");
}

/**
 * @brief 测试通道状态管理
 */
void test_channel_state_management(void)
{
    printf("\n=== Testing Channel State Management ===\n");
    
    // 初始化通道0
    uint8_t result = bsp_hlw8012_init(0, D_NORMAL_MODE);
    TEST_ASSERT(result == HLW8012_OK, "Channel 0 initialization for state test");
    
    // 测试初始状态
    uint8_t state = hlw8012_drv.get_channel_state(0);
    TEST_ASSERT(state == HLW8012_CHANNEL_ENABLED, "Initial channel state is enabled");
    
    // 测试禁用通道
    result = hlw8012_drv.disable_channel(0);
    TEST_ASSERT(result == HLW8012_OK, "Disable channel 0");
    
    state = hlw8012_drv.get_channel_state(0);
    TEST_ASSERT(state == HLW8012_CHANNEL_DISABLED, "Channel 0 is disabled");
    
    // 测试使能通道
    result = hlw8012_drv.enable_channel(0);
    TEST_ASSERT(result == HLW8012_OK, "Enable channel 0");
    
    state = hlw8012_drv.get_channel_state(0);
    TEST_ASSERT(state == HLW8012_CHANNEL_ENABLED, "Channel 0 is enabled");
    
    // 测试无效通道操作
    result = hlw8012_drv.disable_channel(255);
    TEST_ASSERT(result == HLW8012_ERROR_INVALID_CH, "Disable invalid channel");
    
    state = hlw8012_drv.get_channel_state(255);
    TEST_ASSERT(state == HLW8012_CHANNEL_ERROR, "Invalid channel state is error");
}

/**
 * @brief 测试引脚配置
 */
void test_pin_configuration(void)
{
    printf("\n=== Testing Pin Configuration ===\n");
    
    // 测试有效引脚配置
    uint8_t result = hlw8012_drv.set_pin_config(0, GPIOB, GPIO_Pin_10);
    TEST_ASSERT(result == HLW8012_OK, "Valid pin configuration");
    
    // 测试无效通道
    result = hlw8012_drv.set_pin_config(255, GPIOB, GPIO_Pin_10);
    TEST_ASSERT(result == HLW8012_ERROR_INVALID_CH, "Invalid channel pin config");
    
    // 测试无效GPIO
    result = hlw8012_drv.set_pin_config(0, NULL, GPIO_Pin_10);
    TEST_ASSERT(result == HLW8012_ERROR_PARAM, "Invalid GPIO parameter");
}

/**
 * @brief 测试数据读取功能
 */
void test_data_reading(void)
{
    printf("\n=== Testing Data Reading ===\n");
    
    // 初始化通道0
    uint8_t result = bsp_hlw8012_init(0, D_NORMAL_MODE);
    TEST_ASSERT(result == HLW8012_OK, "Channel 0 init for data reading test");
    
    // 测试功率读取（初始值应该为0）
    uint16_t power = bsp_hlw8012_get_channel_power(0);
    TEST_ASSERT(power == 0, "Initial power reading is 0");
    
    // 测试能量读取（初始值应该为0）
    uint16_t energy = bsp_hlw8012_get_channel_energy(0);
    TEST_ASSERT(energy == 0, "Initial energy reading is 0");
    
    // 测试无效通道数据读取
    power = bsp_hlw8012_get_channel_power(255);
    TEST_ASSERT(power == 0, "Invalid channel power reading returns 0");
    
    energy = bsp_hlw8012_get_channel_energy(255);
    TEST_ASSERT(energy == 0, "Invalid channel energy reading returns 0");
    
    // 测试禁用通道数据读取
    hlw8012_drv.disable_channel(0);
    power = bsp_hlw8012_get_channel_power(0);
    TEST_ASSERT(power == 0, "Disabled channel power reading returns 0");
    
    energy = bsp_hlw8012_get_channel_energy(0);
    TEST_ASSERT(energy == 0, "Disabled channel energy reading returns 0");
}

/**
 * @brief 测试能量重置功能
 */
void test_energy_reset(void)
{
    printf("\n=== Testing Energy Reset ===\n");
    
    // 初始化并使能通道0
    bsp_hlw8012_init(0, D_NORMAL_MODE);
    hlw8012_drv.enable_channel(0);
    
    // 模拟设置一些能量值（直接访问内部数据结构用于测试）
    hlw8012_detect_channel[0].energy.U16_AC_E = 100;
    hlw8012_detect_channel[0].energy.U16_E_Pluse_CNT = 50;
    
    // 验证设置的值
    uint16_t energy_before = bsp_hlw8012_get_channel_energy(0);
    TEST_ASSERT(energy_before == 100, "Energy value set correctly");
    
    // 重置能量
    bsp_hlw8012_reset_channel_energy(0);
    
    // 验证重置后的值
    uint16_t energy_after = bsp_hlw8012_get_channel_energy(0);
    TEST_ASSERT(energy_after == 0, "Energy reset to 0");
    TEST_ASSERT(hlw8012_detect_channel[0].energy.U16_E_Pluse_CNT == 0, "Pulse count reset to 0");
}

/**
 * @brief 测试驱动接口完整性
 */
void test_driver_interface(void)
{
    printf("\n=== Testing Driver Interface ===\n");
    
    // 测试所有接口函数指针是否非空
    TEST_ASSERT(hlw8012_drv.init != NULL, "init function pointer");
    TEST_ASSERT(hlw8012_drv.init_all != NULL, "init_all function pointer");
    TEST_ASSERT(hlw8012_drv.exti_callback != NULL, "exti_callback function pointer");
    TEST_ASSERT(hlw8012_drv.timer_callback != NULL, "timer_callback function pointer");
    TEST_ASSERT(hlw8012_drv.get_power != NULL, "get_power function pointer");
    TEST_ASSERT(hlw8012_drv.get_energy != NULL, "get_energy function pointer");
    TEST_ASSERT(hlw8012_drv.enable_channel != NULL, "enable_channel function pointer");
    TEST_ASSERT(hlw8012_drv.disable_channel != NULL, "disable_channel function pointer");
    TEST_ASSERT(hlw8012_drv.get_channel_state != NULL, "get_channel_state function pointer");
    TEST_ASSERT(hlw8012_drv.set_pin_config != NULL, "set_pin_config function pointer");
}

/**
 * @brief 运行所有测试
 */
void run_all_tests(void)
{
    printf("=== HLW8012 Multi-Channel Driver Test Suite ===\n");
    printf("Total channels configured: %d\n", TOTAL_CHARGE_PORT);
    
    test_channel_validity();
    test_driver_initialization();
    test_channel_state_management();
    test_pin_configuration();
    test_data_reading();
    test_energy_reset();
    test_driver_interface();
    
    printf("\n=== Test Results ===\n");
    printf("Tests Passed: %lu\n", test_passed);
    printf("Tests Failed: %lu\n", test_failed);
    printf("Total Tests:  %lu\n", test_passed + test_failed);
    
    if (test_failed == 0) {
        printf("🎉 All tests PASSED! Driver is working correctly.\n");
    } else {
        printf("❌ Some tests FAILED. Please check the implementation.\n");
    }
}

/**
 * @brief 性能测试示例
 */
void performance_test_example(void)
{
    printf("\n=== Performance Test Example ===\n");
    
    // 初始化所有通道
    hlw8012_drv.init_all(D_NORMAL_MODE);
    
    // 测试扫描性能
    uint32_t start_time = 0; // 获取开始时间
    
    for (int i = 0; i < 1000; i++) {
        scan_cf_pins();
    }
    
    uint32_t end_time = 0; // 获取结束时间
    
    printf("1000 scan_cf_pins() calls completed\n");
    printf("Performance test completed (time measurement needs system timer)\n");
}
