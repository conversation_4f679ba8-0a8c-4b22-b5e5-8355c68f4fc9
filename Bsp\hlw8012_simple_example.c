/**
 * @file hlw8012_simple_example.c
 * @brief HLW8012多路驱动简单使用示例
 * <AUTHOR>
 * @date 2024
 */

#include "bsp_hlw8012.h"

/**
 * @brief 简单的HLW8012多路驱动使用示例
 * 
 * 这个文件展示了如何使用简化后的HLW8012多路驱动
 * 只包含最基本的功率和电量检测功能
 */

// 示例：初始化驱动
void example_init(void)
{
    // 初始化所有通道
    bsp_hlw8012_init();
    
    printf("HLW8012 multi-channel driver initialized\n");
}

// 示例：读取单个通道数据
void example_read_single_channel(uint8_t channel)
{
    // 获取功率 (单位: 0.1W)
    uint16_t power = bsp_hlw8012_get_power(channel);
    
    // 获取能量 (单位: 0.01度)
    uint16_t energy = bsp_hlw8012_get_energy(channel);
    
    printf("Channel %d: Power=%d.%dW, Energy=%d.%02dkWh\n", 
           channel, power/10, power%10, energy/100, energy%100);
}

// 示例：读取所有通道数据
void example_read_all_channels(void)
{
    printf("Reading all channels:\n");
    printf("Channel | Power(W) | Energy(kWh)\n");
    printf("--------|----------|------------\n");
    
    for (uint8_t i = 0; i < TOTAL_CHARGE_PORT; i++) {
        uint16_t power = bsp_hlw8012_get_power(i);
        uint16_t energy = bsp_hlw8012_get_energy(i);
        
        printf("   %2d   |  %3d.%d  |   %2d.%02d\n", 
               i, power/10, power%10, energy/100, energy%100);
    }
}

// 示例：轮询模式使用（不使用中断）
void example_polling_mode(void)
{
    // 在主循环中定期调用
    bsp_hlw8012_scan_pins();  // 扫描引脚状态并更新功率值
    
    // 读取数据
    example_read_all_channels();
}

// 示例：中断模式使用
void example_interrupt_mode(void)
{
    // 在1ms定时器中断中调用
    // hlw8012_drv.timer_callback();
    
    // 在GPIO外部中断中调用
    // hlw8012_drv.exti_callback(GPIO_Pin_10); // 示例引脚
    
    // 在主循环中读取数据
    example_read_all_channels();
}

// 示例：完整的使用流程
void example_complete_usage(void)
{
    printf("=== HLW8012 Simple Multi-Channel Driver Example ===\n");
    
    // 1. 初始化驱动
    example_init();
    
    // 2. 读取单个通道
    printf("\nReading channel 0:\n");
    example_read_single_channel(0);
    
    // 3. 读取所有通道
    printf("\nReading all channels:\n");
    example_read_all_channels();
    
    printf("\n=== Example completed ===\n");
}

/**
 * @brief 主循环示例（轮询模式）
 */
void main_loop_polling_example(void)
{
    // 初始化
    bsp_hlw8012_init();
    
    while (1) {
        // 扫描引脚并更新数据
        bsp_hlw8012_scan_pins();
        
        // 每秒打印一次数据
        static uint32_t last_print_time = 0;
        uint32_t current_time = 0; // 获取当前时间，需要根据实际系统实现
        
        if (current_time - last_print_time >= 1000) { // 1秒
            last_print_time = current_time;
            example_read_all_channels();
        }
        
        // 其他任务
        // ...
        
        // 延时
        // delay_ms(10);
    }
}

/**
 * @brief 中断服务程序示例
 */
void timer_interrupt_handler(void)
{
    // 在1ms定时器中断中调用
    hlw8012_drv.timer_callback();
}

void gpio_interrupt_handler(uint16_t gpio_pin)
{
    // 在GPIO外部中断中调用
    hlw8012_drv.exti_callback(gpio_pin);
}

/**
 * @brief 主循环示例（中断模式）
 */
void main_loop_interrupt_example(void)
{
    // 初始化
    bsp_hlw8012_init();
    
    // 配置定时器中断（1ms）
    // setup_timer_interrupt();
    
    // 配置GPIO外部中断
    // setup_gpio_interrupt();
    
    while (1) {
        // 每秒打印一次数据
        static uint32_t last_print_time = 0;
        uint32_t current_time = 0; // 获取当前时间
        
        if (current_time - last_print_time >= 1000) { // 1秒
            last_print_time = current_time;
            example_read_all_channels();
        }
        
        // 其他任务
        // ...
        
        // 延时
        // delay_ms(100);
    }
}

/**
 * @brief 使用说明
 * 
 * 简化后的HLW8012驱动使用非常简单：
 * 
 * 1. 调用 bsp_hlw8012_init() 初始化所有通道
 * 2. 选择使用模式：
 *    - 轮询模式：定期调用 bsp_hlw8012_scan_pins()
 *    - 中断模式：配置定时器和GPIO中断，调用相应的回调函数
 * 3. 使用 bsp_hlw8012_get_power(channel) 获取功率
 * 4. 使用 bsp_hlw8012_get_energy(channel) 获取能量
 * 
 * 注意事项：
 * - 通道号范围：0 到 (TOTAL_CHARGE_PORT-1)
 * - 功率单位：0.1W (例如：返回值100表示10.0W)
 * - 能量单位：0.01度 (例如：返回值150表示1.50度)
 * - 引脚配置在 cf_pins[] 数组中，可根据硬件修改
 */
