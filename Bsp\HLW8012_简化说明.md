# HLW8012多路驱动 - 简化版

## 概述

这是一个简化的HLW8012多路驱动，专门为MCU资源有限的项目设计。只保留最核心的功率和电量检测功能，去除了复杂的状态管理和错误处理机制。

## 主要特点

- **简单易用**：只有4个主要接口函数
- **资源占用少**：最小化内存和CPU使用
- **支持多路**：最多支持10路HLW8012同时工作
- **两种模式**：支持轮询模式和中断模式

## 接口函数

### 1. 初始化
```c
void bsp_hlw8012_init(void);
```
初始化所有通道，设置默认校准参数。

### 2. 获取功率
```c
uint16_t bsp_hlw8012_get_power(uint8_t channel);
```
- **参数**：channel - 通道号 (0 到 9)
- **返回值**：功率值，单位0.1W (例如：100表示10.0W)

### 3. 获取能量
```c
uint16_t bsp_hlw8012_get_energy(uint8_t channel);
```
- **参数**：channel - 通道号 (0 到 9)
- **返回值**：能量值，单位0.01度 (例如：150表示1.50度)

### 4. 扫描引脚（轮询模式）
```c
void bsp_hlw8012_scan_pins(void);
```
扫描所有CF引脚状态变化并更新功率值，用于轮询模式。

## 使用方法

### 轮询模式（推荐用于简单应用）

```c
int main(void)
{
    // 初始化
    bsp_hlw8012_init();
    
    while (1) {
        // 扫描引脚并更新数据
        bsp_hlw8012_scan_pins();
        
        // 读取数据
        for (uint8_t i = 0; i < TOTAL_CHARGE_PORT; i++) {
            uint16_t power = bsp_hlw8012_get_power(i);
            uint16_t energy = bsp_hlw8012_get_energy(i);
            
            printf("CH%d: %d.%dW, %d.%02dkWh\n", 
                   i, power/10, power%10, energy/100, energy%100);
        }
        
        delay_ms(100); // 100ms扫描一次
    }
}
```

### 中断模式（用于高精度应用）

```c
// 1ms定时器中断
void TIM_IRQHandler(void)
{
    hlw8012_drv.timer_callback();
}

// GPIO外部中断
void EXTI_IRQHandler(void)
{
    uint16_t pin = get_interrupt_pin(); // 获取中断引脚
    hlw8012_drv.exti_callback(pin);
}

int main(void)
{
    // 初始化
    bsp_hlw8012_init();
    
    // 配置中断
    setup_timer_interrupt(); // 1ms定时器
    setup_gpio_interrupt();  // CF引脚中断
    
    while (1) {
        // 读取数据
        uint16_t power = bsp_hlw8012_get_power(0);
        uint16_t energy = bsp_hlw8012_get_energy(0);
        
        // 处理数据...
        
        delay_ms(1000);
    }
}
```

## 引脚配置

在 `bsp_hlw8012.c` 中的 `cf_pins[]` 数组定义了每个通道对应的GPIO引脚：

```c
static const t_cf_pin_config cf_pins[TOTAL_CHARGE_PORT] = {
    {GPIOB, GPIO_Pin_10}, // 通道0
    {GPIOB, GPIO_Pin_11}, // 通道1
    {GPIOB, GPIO_Pin_12}, // 通道2
    // ... 更多通道
};
```

根据实际硬件连接修改这个数组。

## 校准参数

默认校准参数在 `init_calibration_data()` 函数中设置：

```c
local_cal_data.refer_power = 1000;    // 参考功率1000W
local_cal_data.cal_freq[i] = 4975;    // 频率校准值4975us
local_cal_data.cal_eng[i] = 360;      // 能量校准值360脉冲/0.01度
```

可根据实际HLW8012芯片的特性调整这些参数。

## 资源占用

- **RAM使用**：约 `sizeof(t_hlw8012_data) * TOTAL_CHARGE_PORT` ≈ 300字节（10通道）
- **Flash使用**：约2KB代码空间
- **CPU占用**：轮询模式下每100ms扫描一次，占用很少

## 注意事项

1. **通道数量**：通过 `TOTAL_CHARGE_PORT` 宏定义，默认10路
2. **引脚配置**：需要根据实际硬件修改 `cf_pins[]` 数组
3. **校准精度**：使用默认校准参数，精度可能不够高，建议实际校准
4. **中断优先级**：如果使用中断模式，注意设置合适的中断优先级
5. **线程安全**：如果在多线程环境使用，需要添加互斥保护

## 与原版本的区别

| 功能 | 原版本 | 简化版 |
|------|--------|--------|
| 通道管理 | 复杂的状态管理 | 简单的数组访问 |
| 错误处理 | 完整的错误代码 | 基本的参数检查 |
| 接口数量 | 15+个函数 | 4个核心函数 |
| 内存占用 | 较大 | 最小化 |
| 代码复杂度 | 高 | 低 |
| 适用场景 | 复杂应用 | 简单应用 |

## 示例代码

详细的使用示例请参考 `hlw8012_simple_example.c` 文件。

这个简化版本专注于核心功能，适合资源受限的MCU项目使用。
