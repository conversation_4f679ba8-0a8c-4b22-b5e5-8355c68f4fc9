#include "bsp_hlw8012.h"

// 位操作宏定义
#define GET_BIT_N(data, n) ((data >> n) & 0x01)

// 校准数据结构定义
typedef struct {
    uint16_t cal_freq[TOTAL_CHARGE_PORT]; // 通道频率校准值
    uint16_t cal_eng[TOTAL_CHARGE_PORT];  // 通道能量校准值
    uint16_t overflow_flag;               // 溢出标志
    uint16_t refer_power;                 // 参考功率，一般为1000W
} t_cal_data;

// 全局变量定义
static t_cal_data local_cal_data = {0};
t_hlw8012_data hlw8012_detect_channel[TOTAL_CHARGE_PORT];

// 引脚配置结构体
typedef struct {
    GPIO_TypeDef *gpio;
    uint16_t pin;
} t_cf_pin_config;

// 每个通道的对应引脚配置
static const t_cf_pin_config cf_pins[TOTAL_CHARGE_PORT] = {
    {GPIOB, GPIO_Pin_10}, // 通道1
    {GPIOB, GPIO_Pin_11}, // 通道2
    {GPIOB, GPIO_Pin_12}, // 通道3
    {GPIOB, GPIO_Pin_13}, // 通道4
    {GPIOB, GPIO_Pin_14}, // 通道5
    {GPIOB, GPIO_Pin_15}, // 通道6
    {GPIOC, GPIO_Pin_6},  // 通道7
    {GPIOC, GPIO_Pin_7},  // 通道8
    {GPIOC, GPIO_Pin_8},  // 通道9
    {GPIOC, GPIO_Pin_9}   // 通道10
};

// 引脚状态跟踪
typedef struct {
    uint8_t prev_state : 1;
    uint8_t curr_state : 1;
} t_pin_state;

static t_pin_state cf_pin_states[TOTAL_CHARGE_PORT];

/**
 * @brief 初始化单个通道
 *
 * @param channel  通道号
 */
static void hlw8012_init_channel(uint8_t channel)
{
    if (channel >= TOTAL_CHARGE_PORT) {
        return;
    }

    uint16_t freq_temp   = local_cal_data.cal_freq[channel];
    uint16_t energy_temp = local_cal_data.cal_eng[channel];
    uint16_t over_temp   = local_cal_data.overflow_flag;

    if (freq_temp != 0) {
        if (GET_BIT_N(over_temp, channel) != 0)
            hlw8012_detect_channel[channel].refrence.U32_P_REF_PLUSEWIDTH_TIME = freq_temp + 0xffff;
        else
            hlw8012_detect_channel[channel].refrence.U32_P_REF_PLUSEWIDTH_TIME = freq_temp;
    }

    hlw8012_detect_channel[channel].refrence.U16_REF_001_E_Pluse_CNT = energy_temp;
    hlw8012_detect_channel[channel].WorkMode                         = D_NORMAL_MODE;

    // 初始化功率相关参数
    hlw8012_detect_channel[channel].power.U16_AC_P                   = 0;
    hlw8012_detect_channel[channel].power.U16_P_TotalTimes           = 0;
    hlw8012_detect_channel[channel].power.U16_P_OneCycleTime         = 0;
    hlw8012_detect_channel[channel].power.U16_P_Last_OneCycleTime    = 0;
    hlw8012_detect_channel[channel].power.U16_P_CNT                  = 0;
    hlw8012_detect_channel[channel].power.U16_P_Last_CNT             = 0;
    hlw8012_detect_channel[channel].power.B_P_TestOneCycle_Mode      = 1;
    hlw8012_detect_channel[channel].power.B_P_Last_TestOneCycle_Mode = 1;
    hlw8012_detect_channel[channel].power.B_P_OVERFLOW               = 1;
    hlw8012_detect_channel[channel].power.B_P_Last_OVERFLOW          = 1;

    // 初始化能量相关参数
    hlw8012_detect_channel[channel].energy.U16_AC_E        = 0;
    hlw8012_detect_channel[channel].energy.U16_E_Pluse_CNT = 0;
}

/**
 * @brief Get the cf pin interrupt object
 * 
 * @param channel 
 */
static void get_cf_pin_interrupt(uint8_t channel)
{
    if (channel >= TOTAL_CHARGE_PORT) {
        return;
    }

    // 功率测量
    hlw8012_detect_channel[channel].power.U16_P_TotalTimes = 0; // 完成一次有效的测量，溢出寄存器清零
    hlw8012_detect_channel[channel].power.U16_P_CNT++;
    if (hlw8012_detect_channel[channel].power.B_P_OVERFLOW != 0) {
        // 从溢出模式转入,开始测量
        hlw8012_detect_channel[channel].power.B_P_TestOneCycle_Mode = 0; // 初始化为计数脉冲测量模式
        hlw8012_detect_channel[channel].power.U16_P_TotalTimes      = 0; // 清溢出寄存器清零
        hlw8012_detect_channel[channel].power.U16_P_OneCycleTime    = 0; // 清测量寄存器
        hlw8012_detect_channel[channel].power.U16_P_CNT             = 1;
        hlw8012_detect_channel[channel].power.B_P_OVERFLOW          = 0; // 清溢出标志位
    } else {
        if (hlw8012_detect_channel[channel].power.B_P_TestOneCycle_Mode == 1) {
            if (hlw8012_detect_channel[channel].power.U16_P_OneCycleTime >= D_TIME1_100MS) {
                // 单周期测量模式
                hlw8012_detect_channel[channel].power.U16_P_Last_OneCycleTime    = hlw8012_detect_channel[channel].power.U16_P_OneCycleTime;
                hlw8012_detect_channel[channel].power.B_P_Last_TestOneCycle_Mode = hlw8012_detect_channel[channel].power.B_P_TestOneCycle_Mode;
                hlw8012_detect_channel[channel].power.B_P_OVERFLOW               = 0; // 溢出标志位清零
                hlw8012_detect_channel[channel].power.B_P_Last_OVERFLOW          = hlw8012_detect_channel[channel].power.B_P_OVERFLOW;
                // 清状态参数,重新开始测试
                hlw8012_detect_channel[channel].power.B_P_TestOneCycle_Mode = 0; // 初始化为计数脉冲测量模式
                hlw8012_detect_channel[channel].power.U16_P_TotalTimes      = 0; // 完成一次有效的测量，溢出寄存器清零
                hlw8012_detect_channel[channel].power.U16_P_OneCycleTime    = 0; // 清测量寄存器
                hlw8012_detect_channel[channel].power.U16_P_CNT             = 1;
            }
        } else {
            if (hlw8012_detect_channel[channel].power.U16_P_OneCycleTime >= D_TIME1_3S) {
                hlw8012_detect_channel[channel].power.U16_P_Last_OneCycleTime    = hlw8012_detect_channel[channel].power.U16_P_OneCycleTime;
                hlw8012_detect_channel[channel].power.U16_P_Last_CNT             = hlw8012_detect_channel[channel].power.U16_P_CNT;
                hlw8012_detect_channel[channel].power.B_P_Last_TestOneCycle_Mode = hlw8012_detect_channel[channel].power.B_P_TestOneCycle_Mode;
                hlw8012_detect_channel[channel].power.B_P_OVERFLOW               = 0; // 溢出标志位清零
                hlw8012_detect_channel[channel].power.B_P_Last_OVERFLOW          = hlw8012_detect_channel[channel].power.B_P_OVERFLOW;
                // 清状态参数,重新开始测试
                hlw8012_detect_channel[channel].power.B_P_TestOneCycle_Mode = 0; // 初始化为计数脉冲测量模式
                hlw8012_detect_channel[channel].power.U16_P_TotalTimes      = 0; // 完成一次有效的测量，溢出寄存器清零
                hlw8012_detect_channel[channel].power.U16_P_OneCycleTime    = 0; // 清测量寄存器
                hlw8012_detect_channel[channel].power.U16_P_CNT             = 1;
            }
        }
    }

    // 校正模式
    if (hlw8012_detect_channel[channel].WorkMode == D_CAL_START_MODE) {
        // 记录单位时间内的用电量
        hlw8012_detect_channel[channel].energy.U16_E_Pluse_CNT++;
    }

    // 用电量计量，每0.01度电，用电量寄存器增加0.01度
    if (hlw8012_detect_channel[channel].WorkMode == D_NORMAL_MODE) {
        hlw8012_detect_channel[channel].energy.U16_E_Pluse_CNT++;
        if (hlw8012_detect_channel[channel].energy.U16_E_Pluse_CNT == hlw8012_detect_channel[channel].refrence.U16_REF_001_E_Pluse_CNT) {
            hlw8012_detect_channel[channel].energy.U16_E_Pluse_CNT = 0;
            hlw8012_detect_channel[channel].energy.U16_AC_E++;
        }
    }
}

static void update_channel_time(uint8_t channel)
{
    if (channel >= TOTAL_CHARGE_PORT) {
        return;
    }

    // 能量计量，每0.01度电，用电量寄存器增加0.01度
    if (hlw8012_detect_channel[channel].WorkMode == D_NORMAL_MODE) {
        hlw8012_detect_channel[channel].energy.U16_E_Pluse_CNT++;
        if (hlw8012_detect_channel[channel].energy.U16_E_Pluse_CNT >= hlw8012_detect_channel[channel].refrence.U16_REF_001_E_Pluse_CNT) {
            hlw8012_detect_channel[channel].energy.U16_E_Pluse_CNT = 0;
            hlw8012_detect_channel[channel].energy.U16_AC_E++;
        }
    }

    // 功率测量
    if (hlw8012_detect_channel[channel].power.U16_P_CNT != 0) {
        hlw8012_detect_channel[channel].power.U16_P_OneCycleTime++;
        hlw8012_detect_channel[channel].power.U16_P_TotalTimes++;
    }
    if (hlw8012_detect_channel[channel].power.U16_P_TotalTimes >= D_TIME1_P_OVERFLOW) {
        hlw8012_detect_channel[channel].power.B_P_OVERFLOW      = 1; // 溢出，
        hlw8012_detect_channel[channel].power.B_P_Last_OVERFLOW = hlw8012_detect_channel[channel].power.B_P_OVERFLOW;
        // 清状态参数,重新开始测试
        hlw8012_detect_channel[channel].power.U16_P_TotalTimes      = 0; // 清溢出寄存器
        hlw8012_detect_channel[channel].power.U16_P_OneCycleTime    = 0;
        hlw8012_detect_channel[channel].power.U16_P_CNT             = 0; // 等待下一次中断开始计数
        hlw8012_detect_channel[channel].power.B_P_TestOneCycle_Mode = 0; // 初始化为计数脉冲测量模式
    } else if (hlw8012_detect_channel[channel].power.U16_P_OneCycleTime == D_TIME1_100MS) {
        if (hlw8012_detect_channel[channel].power.U16_P_CNT < 2) {
            // 100ms内只有一次中断，说明周期>100ms,采用单周期测量模式
            hlw8012_detect_channel[channel].power.B_P_TestOneCycle_Mode = 1;
        } else {
            // 100ms内有2次或以上数量脉冲，说明周期<100ms，采用计数脉冲测量模式
            hlw8012_detect_channel[channel].power.B_P_TestOneCycle_Mode = 0;
        }
    }
}

/*-------------------------------------------- 功率、电压、电流计算 -------------------------------------------*/
/*=====================================================
 * Function : uint16_t get_channel_power(uint8_t channel)
 * Describe : 获取通道功率
 * Input    : channel - 通道号
 * Output   : none
 * Return   : 功率值
 * Record   : 2014/04/14
=====================================================*/
static uint16_t get_channel_power(uint8_t channel)
{
    if (channel >= TOTAL_CHARGE_PORT) {
        return 0;
    }

    uint16_t power_value = 0;
    uint32_t a;
    uint32_t b;
    uint32_t u32_P_Period;

    if (hlw8012_detect_channel[channel].power.B_P_Last_TestOneCycle_Mode == 1) {
        // 单周期测量模式
        b = hlw8012_detect_channel[channel].power.U16_P_Last_OneCycleTime;
        b *= 1000; // ms转换成us
        u32_P_Period = b;
    } else {
        // 计数脉冲测量模式
        b = hlw8012_detect_channel[channel].power.U16_P_Last_OneCycleTime;
        b *= 1000;
        u32_P_Period = b / (hlw8012_detect_channel[channel].power.U16_P_Last_CNT - 1);
    }

    hlw8012_detect_channel[channel].power.U32_P_CURRENT_PLUSEWIDTH_TIME = u32_P_Period; // 校正时取U32_P_CURRENT_PLUSEWIDTH_TIME参数作为参考值
    a                                                                   = local_cal_data.refer_power * hlw8012_detect_channel[channel].refrence.U32_P_REF_PLUSEWIDTH_TIME;

    // 避免除零错误
    if (hlw8012_detect_channel[channel].power.U32_P_CURRENT_PLUSEWIDTH_TIME == 0) {
        power_value = 0;
    } else {
        power_value = a / hlw8012_detect_channel[channel].power.U32_P_CURRENT_PLUSEWIDTH_TIME;
    }

    if (power_value == 0xffff) { // 计算溢出
        power_value = 0;
    }

    if (hlw8012_detect_channel[channel].power.B_P_Last_OVERFLOW != 0) {
        power_value = 0;
    }

    return power_value;
}

/*=====================================================
 * Function : uint16_t get_channel_energy(uint8_t channel)
 * Describe : 获取通道能量
 * Input    : channel - 通道号
 * Output   : none
 * Return   : 能量值，单位0.01度
 * Record   : 2014/04/14
=====================================================*/
static uint16_t get_channel_energy(uint8_t channel)
{
    if (channel >= TOTAL_CHARGE_PORT) {
        return 0;
    }

    return hlw8012_detect_channel[channel].energy.U16_AC_E;
}

// 获取中断引脚对应的通道号
static uint8_t get_channel_from_pin(uint16_t gpio_pin)
{
    for (uint8_t i = 0; i < TOTAL_CHARGE_PORT; i++) {
        if (cf_pins[i].pin == gpio_pin) {
            return i;
        }
    }
    return 0xFF; // 无效通道
}

// 定时器中断回调函数
static void timer_irq_callback(void)
{
    // 更新所有通道的时间
    for (uint8_t i = 0; i < TOTAL_CHARGE_PORT; i++) {
        update_channel_time(i);
    }
}

// 外部中断回调函数
static void exti_irq_callback(uint16_t GPIO_Pin)
{
    uint8_t channel = get_channel_from_pin(GPIO_Pin);

    if (channel < TOTAL_CHARGE_PORT) {
        // 处理CF引脚中断（功率脉冲）
        get_cf_pin_interrupt(channel);
    }
}

// 更新所有通道的功率值
static void update_all_power(void)
{
    for (uint8_t i = 0; i < TOTAL_CHARGE_PORT; i++) {
        uint16_t power_value                     = get_channel_power(i);
        hlw8012_detect_channel[i].power.U16_AC_P = power_value;
    }
}

// 扫描所有CF引脚的状态变化（轮询模式）
static void scan_cf_pins(void)
{
    for (uint8_t i = 0; i < TOTAL_CHARGE_PORT; i++) {
        // 读取当前引脚状态
        uint8_t current_state = 1; // 默认高电平

        if (cf_pins[i].gpio == GPIOB) {
            current_state = (GPIOB->IDR & cf_pins[i].pin) ? 1 : 0;
        } else if (cf_pins[i].gpio == GPIOC) {
            current_state = (GPIOC->IDR & cf_pins[i].pin) ? 1 : 0;
        }

        cf_pin_states[i].curr_state = current_state;

        // 检测下降沿 (从高电平到低电平)
        if (cf_pin_states[i].prev_state == 1 && cf_pin_states[i].curr_state == 0) {
            // 引脚状态从高到低，触发中断处理
            get_cf_pin_interrupt(i);
        }

        // 更新上一次状态
        cf_pin_states[i].prev_state = cf_pin_states[i].curr_state;
    }
}

// 初始化校准数据
static void init_calibration_data(void)
{
    // 设置默认校准值
    local_cal_data.refer_power = 1000; // 参考功率默认为1000W

    // 初始化每个通道的校准数据
    for (uint8_t i = 0; i < TOTAL_CHARGE_PORT; i++) {
        local_cal_data.cal_freq[i] = 4975; // 默认周期值，4975us
        local_cal_data.cal_eng[i]  = 360;  // 默认0.01度电脉冲数
    }

    local_cal_data.overflow_flag = 0;
}

// 简化的接口函数实现

// 初始化所有通道
void bsp_hlw8012_init(void)
{
    // 初始化校准数据
    init_calibration_data();

    // 初始化所有通道
    for (uint8_t i = 0; i < TOTAL_CHARGE_PORT; i++) {
        hlw8012_init_channel(i);
    }

    // 初始化引脚状态
    for (uint8_t i = 0; i < TOTAL_CHARGE_PORT; i++) {
        cf_pin_states[i].prev_state = 1; // 默认为高电平
        cf_pin_states[i].curr_state = 1;
    }
}

// 获取通道功率
uint16_t bsp_hlw8012_get_power(uint8_t channel)
{
    return get_channel_power(channel);
}

// 获取通道能量
uint16_t bsp_hlw8012_get_energy(uint8_t channel)
{
    return get_channel_energy(channel);
}

// 扫描引脚状态（轮询模式使用）
void bsp_hlw8012_scan_pins(void)
{
    scan_cf_pins();
    update_all_power();
}

// 导出的驱动函数集
const t_hlw8012_drv hlw8012_drv = {
    .init           = bsp_hlw8012_init,
    .exti_callback  = exti_irq_callback,
    .timer_callback = timer_irq_callback,
    .get_power      = bsp_hlw8012_get_power,
    .get_energy     = bsp_hlw8012_get_energy};
