#include "bsp_hlw8012.h"

// 位操作宏定义
#define GET_BIT_N(data, n) ((data >> n) & 0x01)

// 简化的全局变量
t_hlw8012_data hlw8012_detect_channel[TOTAL_CHARGE_PORT];

// 引脚配置结构体
typedef struct {
    GPIO_TypeDef *gpio;
    uint16_t pin;
} t_cf_pin_config;

// 每个通道的对应引脚配置
static const t_cf_pin_config cf_pins[TOTAL_CHARGE_PORT] = {
    {GPIOB, GPIO_Pin_10}, // 通道1
    {GPIOB, GPIO_Pin_11}, // 通道2
    {GPIOB, GPIO_Pin_12}, // 通道3
    {GPIOB, GPIO_Pin_13}, // 通道4
    {GPIOB, GPIO_Pin_14}, // 通道5
    {GPIOB, GPIO_Pin_15}, // 通道6
    {GPIOC, GPIO_Pin_6},  // 通道7
    {GPIOC, GPIO_Pin_7},  // 通道8
    {GPIOC, GPIO_Pin_8},  // 通道9
    {GPIOC, GPIO_Pin_9}   // 通道10
};

// 引脚状态跟踪
typedef struct {
    uint8_t prev_state : 1;
    uint8_t curr_state : 1;
} t_pin_state;

static t_pin_state cf_pin_states[TOTAL_CHARGE_PORT];

// 超简化的通道初始化
static void hlw8012_init_channel(uint8_t channel)
{
    if (channel >= TOTAL_CHARGE_PORT) {
        return;
    }

    // 清零所有数据
    hlw8012_detect_channel[channel].power.U16_P_OneCycleTime = 0;
    hlw8012_detect_channel[channel].power.U16_P_CNT          = 0;
    hlw8012_detect_channel[channel].power.U16_AC_P           = 0;

    hlw8012_detect_channel[channel].energy.U16_AC_E        = 0;
    hlw8012_detect_channel[channel].energy.U16_E_Pluse_CNT = 0;
}

// 简化的CF引脚中断处理
static void get_cf_pin_interrupt(uint8_t channel)
{
    if (channel >= TOTAL_CHARGE_PORT) {
        return;
    }

    // 简单的脉冲计数
    hlw8012_detect_channel[channel].power.U16_P_CNT++;

    // 能量累计（每个脉冲累计能量）
    hlw8012_detect_channel[channel].energy.U16_E_Pluse_CNT++;
    if (hlw8012_detect_channel[channel].energy.U16_E_Pluse_CNT >= 360) { // 360脉冲=0.01度
        hlw8012_detect_channel[channel].energy.U16_E_Pluse_CNT = 0;
        hlw8012_detect_channel[channel].energy.U16_AC_E++;
    }
}

// 简化的时间更新（1ms定时器调用）
static void update_channel_time(uint8_t channel)
{
    if (channel >= TOTAL_CHARGE_PORT) {
        return;
    }

    // 简单的时间计数
    hlw8012_detect_channel[channel].power.U16_P_OneCycleTime++;

    // 超时清零（5秒无脉冲认为功率为0）
    if (hlw8012_detect_channel[channel].power.U16_P_OneCycleTime >= 5000) {
        hlw8012_detect_channel[channel].power.U16_P_CNT          = 0;
        hlw8012_detect_channel[channel].power.U16_P_OneCycleTime = 0;
    }
}

/*-------------------------------------------- 功率、电压、电流计算 -------------------------------------------*/
/*=====================================================
 * Function : uint16_t get_channel_power(uint8_t channel)
 * Describe : 获取通道功率
 * Input    : channel - 通道号
 * Output   : none
 * Return   : 功率值
 * Record   : 2014/04/14
=====================================================*/
// 超简化的功率计算
static uint16_t get_channel_power(uint8_t channel)
{
    if (channel >= TOTAL_CHARGE_PORT) {
        return 0;
    }

    // 简单的频率计算：功率 = 脉冲数 * 校准系数
    uint16_t pulse_count = hlw8012_detect_channel[channel].power.U16_P_CNT;
    uint16_t time_ms     = hlw8012_detect_channel[channel].power.U16_P_OneCycleTime;

    if (time_ms == 0 || pulse_count == 0) {
        return 0;
    }

    // 简化计算：功率 = (脉冲数 * 1000 * 校准系数) / 时间ms
    // 校准系数可以通过实际测试调整
    uint32_t power = (uint32_t)pulse_count * 1000 * 10 / time_ms; // 10是经验校准系数

    if (power > 65535) {
        power = 65535;
    }

    return (uint16_t)power;
}

/*=====================================================
 * Function : uint16_t get_channel_energy(uint8_t channel)
 * Describe : 获取通道能量
 * Input    : channel - 通道号
 * Output   : none
 * Return   : 能量值，单位0.01度
 * Record   : 2014/04/14
=====================================================*/
static uint16_t get_channel_energy(uint8_t channel)
{
    if (channel >= TOTAL_CHARGE_PORT) {
        return 0;
    }

    return hlw8012_detect_channel[channel].energy.U16_AC_E;
}

// 获取中断引脚对应的通道号
static uint8_t get_channel_from_pin(uint16_t gpio_pin)
{
    for (uint8_t i = 0; i < TOTAL_CHARGE_PORT; i++) {
        if (cf_pins[i].pin == gpio_pin) {
            return i;
        }
    }
    return 0xFF; // 无效通道
}

// 定时器中断回调函数
static void timer_irq_callback(void)
{
    // 更新所有通道的时间
    for (uint8_t i = 0; i < TOTAL_CHARGE_PORT; i++) {
        update_channel_time(i);
    }
}

// 外部中断回调函数
static void exti_irq_callback(uint16_t GPIO_Pin)
{
    uint8_t channel = get_channel_from_pin(GPIO_Pin);

    if (channel < TOTAL_CHARGE_PORT) {
        // 处理CF引脚中断（功率脉冲）
        get_cf_pin_interrupt(channel);
    }
}

// 更新所有通道的功率值
static void update_all_power(void)
{
    for (uint8_t i = 0; i < TOTAL_CHARGE_PORT; i++) {
        uint16_t power_value                     = get_channel_power(i);
        hlw8012_detect_channel[i].power.U16_AC_P = power_value;
    }
}

// 扫描所有CF引脚的状态变化（轮询模式）
static void scan_cf_pins(void)
{
    for (uint8_t i = 0; i < TOTAL_CHARGE_PORT; i++) {
        // 读取当前引脚状态
        uint8_t current_state = 1; // 默认高电平

        if (cf_pins[i].gpio == GPIOB) {
            current_state = (GPIOB->IDR & cf_pins[i].pin) ? 1 : 0;
        } else if (cf_pins[i].gpio == GPIOC) {
            current_state = (GPIOC->IDR & cf_pins[i].pin) ? 1 : 0;
        }

        cf_pin_states[i].curr_state = current_state;

        // 检测下降沿 (从高电平到低电平)
        if (cf_pin_states[i].prev_state == 1 && cf_pin_states[i].curr_state == 0) {
            // 引脚状态从高到低，触发中断处理
            get_cf_pin_interrupt(i);
        }

        // 更新上一次状态
        cf_pin_states[i].prev_state = cf_pin_states[i].curr_state;
    }
}

// 简化的接口函数实现

// 超简化的初始化
void bsp_hlw8012_init(void)
{
    // 初始化所有通道
    for (uint8_t i = 0; i < TOTAL_CHARGE_PORT; i++) {
        hlw8012_init_channel(i);
        cf_pin_states[i].prev_state = 1; // 默认为高电平
        cf_pin_states[i].curr_state = 1;
    }
}

// 获取通道功率
uint16_t bsp_hlw8012_get_power(uint8_t channel)
{
    return get_channel_power(channel);
}

// 获取通道能量
uint16_t bsp_hlw8012_get_energy(uint8_t channel)
{
    return get_channel_energy(channel);
}

// 扫描引脚状态（轮询模式使用）
void bsp_hlw8012_scan_pins(void)
{
    scan_cf_pins();
    update_all_power();
}

// 导出的驱动函数集
const t_hlw8012_drv hlw8012_drv = {
    .init           = bsp_hlw8012_init,
    .exti_callback  = exti_irq_callback,
    .timer_callback = timer_irq_callback,
    .get_power      = bsp_hlw8012_get_power,
    .get_energy     = bsp_hlw8012_get_energy};
