#include "bsp_hlw8012.h"

// 位操作宏定义
#define GET_BIT_N(data, n) ((data >> n) & 0x01)
#define SET_BIT_N(data, n) ((data) |= (1 << (n)))
#define CLR_BIT_N(data, n) ((data) &= ~(1 << (n)))

// 校准数据结构定义
typedef struct {
    uint16_t cal_freq[TOTAL_CHARGE_PORT]; // 通道频率校准值
    uint16_t cal_eng[TOTAL_CHARGE_PORT];  // 通道能量校准值
    uint16_t overflow_flag;               // 溢出标志
    uint16_t refer_power;                 // 参考功率，一般为1000W
} t_cal_data;

// 校准状态结构定义
typedef struct {
    uint8_t calibrate_complete; // 校准完成标志
} t_calibrating_data;

// 全局变量定义
static t_cal_data local_cal_data           = {0};
static t_calibrating_data calibrating_data = {0};

t_hlw8012_data hlw8012_detect_channel[TOTAL_CHARGE_PORT];

// 每个通道的对应引脚配置，支持动态配置
static t_hlw8012_pin_config cf_pins[TOTAL_CHARGE_PORT] = {
    {GPIOB, GPIO_Pin_10, 1}, // 通道1，默认使能
    {GPIOB, GPIO_Pin_11, 1}, // 通道2，默认使能
    {GPIOB, GPIO_Pin_12, 1}, // 通道3，默认使能
    {GPIOB, GPIO_Pin_13, 1}, // 通道4，默认使能
    {GPIOB, GPIO_Pin_14, 1}, // 通道5，默认使能
    {GPIOB, GPIO_Pin_15, 1}, // 通道6，默认使能
    {GPIOC, GPIO_Pin_6,  1}, // 通道7，默认使能
    {GPIOC, GPIO_Pin_7,  1}, // 通道8，默认使能
    {GPIOC, GPIO_Pin_8,  1}, // 通道9，默认使能
    {GPIOC, GPIO_Pin_9,  1}  // 通道10，默认使能
};

// 驱动初始化状态
static uint8_t hlw8012_driver_initialized = 0;

// 引脚状态跟踪
typedef struct {
    uint8_t prev_state : 1;
    uint8_t curr_state : 1;
} t_pin_state;

static t_pin_state cf_pin_states[TOTAL_CHARGE_PORT];

// 内部函数声明
static uint8_t is_channel_valid(uint8_t channel);
static void reset_channel_data(uint8_t channel);
static uint32_t get_system_tick(void);

// 检查通道是否有效
static uint8_t is_channel_valid(uint8_t channel)
{
    return (channel < TOTAL_CHARGE_PORT) ? 1 : 0;
}

// 重置通道数据
static void reset_channel_data(uint8_t channel)
{
    if (!is_channel_valid(channel)) {
        return;
    }

    // 重置功率相关参数
    hlw8012_detect_channel[channel].power.U16_AC_P                   = 0;
    hlw8012_detect_channel[channel].power.U16_P_TotalTimes           = 0;
    hlw8012_detect_channel[channel].power.U16_P_OneCycleTime         = 0;
    hlw8012_detect_channel[channel].power.U16_P_Last_OneCycleTime    = 0;
    hlw8012_detect_channel[channel].power.U16_P_CNT                  = 0;
    hlw8012_detect_channel[channel].power.U16_P_Last_CNT             = 0;
    hlw8012_detect_channel[channel].power.B_P_TestOneCycle_Mode      = 1;
    hlw8012_detect_channel[channel].power.B_P_Last_TestOneCycle_Mode = 1;
    hlw8012_detect_channel[channel].power.B_P_OVERFLOW               = 1;
    hlw8012_detect_channel[channel].power.B_P_Last_OVERFLOW          = 1;

    // 重置能量相关参数
    hlw8012_detect_channel[channel].energy.U16_AC_E        = 0;
    hlw8012_detect_channel[channel].energy.U16_E_Pluse_CNT = 0;

    // 重置时间参数
    hlw8012_detect_channel[channel].time.U16_Cal_Times = 0;

    // 重置状态
    hlw8012_detect_channel[channel].error_code = HLW8012_OK;
    hlw8012_detect_channel[channel].last_update_time = get_system_tick();
}

// 获取系统时间戳（简单实现，可根据实际系统调整）
static uint32_t get_system_tick(void)
{
    // 这里应该返回系统时间戳，可以使用FreeRTOS的xTaskGetTickCount()
    // 或者其他系统时间函数
    return 0; // 临时实现
}

// hlw8012参数初始化
static uint8_t hlw8012_init(uint8_t channel, uint8_t mode)
{
    uint16_t freq_temp = 0, energy_temp = 0, over_temp = 0;

    if (!is_channel_valid(channel)) {
        return HLW8012_ERROR_INVALID_CH;
    }

    freq_temp   = local_cal_data.cal_freq[channel];
    energy_temp = local_cal_data.cal_eng[channel];
    over_temp   = local_cal_data.overflow_flag;

    if (freq_temp != 0) {
        if (GET_BIT_N(over_temp, channel) != 0)
            hlw8012_detect_channel[channel].refrence.U32_P_REF_PLUSEWIDTH_TIME = freq_temp + 0xffff;
        else
            hlw8012_detect_channel[channel].refrence.U32_P_REF_PLUSEWIDTH_TIME = freq_temp;
    }

    hlw8012_detect_channel[channel].refrence.U16_REF_001_E_Pluse_CNT = energy_temp;

    hlw8012_detect_channel[channel].WorkMode = mode;

    // 重置通道数据
    reset_channel_data(channel);

    // 设置通道状态为使能
    hlw8012_detect_channel[channel].channel_state = HLW8012_CHANNEL_ENABLED;

    return HLW8012_OK;
}

static void get_cf_pin_interrupt(uint8_t channel)
{
    if (!is_channel_valid(channel)) {
        return;
    }

    // 检查通道是否使能
    if (hlw8012_detect_channel[channel].channel_state != HLW8012_CHANNEL_ENABLED) {
        return;
    }

    // 更新最后更新时间
    hlw8012_detect_channel[channel].last_update_time = get_system_tick();

    // 功率测量
    hlw8012_detect_channel[channel].power.U16_P_TotalTimes = 0; // 完成一次有效的测量，溢出寄存器清零
    hlw8012_detect_channel[channel].power.U16_P_CNT++;
    if (hlw8012_detect_channel[channel].power.B_P_OVERFLOW != 0) {
        // 从溢出模式转入,开始测量
        hlw8012_detect_channel[channel].power.B_P_TestOneCycle_Mode = 0; // 初始化为计数脉冲测量模式
        hlw8012_detect_channel[channel].power.U16_P_TotalTimes      = 0; // 清溢出寄存器清零
        hlw8012_detect_channel[channel].power.U16_P_OneCycleTime    = 0; // 清测量寄存器
        hlw8012_detect_channel[channel].power.U16_P_CNT             = 1;
        hlw8012_detect_channel[channel].power.B_P_OVERFLOW          = 0; // 清溢出标志位
    } else {
        if (hlw8012_detect_channel[channel].power.B_P_TestOneCycle_Mode == 1) {
            if (hlw8012_detect_channel[channel].power.U16_P_OneCycleTime >= D_TIME1_100MS) {
                // 单周期测量模式
                hlw8012_detect_channel[channel].power.U16_P_Last_OneCycleTime    = hlw8012_detect_channel[channel].power.U16_P_OneCycleTime;
                hlw8012_detect_channel[channel].power.B_P_Last_TestOneCycle_Mode = hlw8012_detect_channel[channel].power.B_P_TestOneCycle_Mode;
                hlw8012_detect_channel[channel].power.B_P_OVERFLOW               = 0; // 溢出标志位清零
                hlw8012_detect_channel[channel].power.B_P_Last_OVERFLOW          = hlw8012_detect_channel[channel].power.B_P_OVERFLOW;
                // 清状态参数,重新开始测试
                hlw8012_detect_channel[channel].power.B_P_TestOneCycle_Mode = 0; // 初始化为计数脉冲测量模式
                hlw8012_detect_channel[channel].power.U16_P_TotalTimes      = 0; // 完成一次有效的测量，溢出寄存器清零
                hlw8012_detect_channel[channel].power.U16_P_OneCycleTime    = 0; // 清测量寄存器
                hlw8012_detect_channel[channel].power.U16_P_CNT             = 1;
            }
        } else {
            if (hlw8012_detect_channel[channel].power.U16_P_OneCycleTime >= D_TIME1_3S) {
                hlw8012_detect_channel[channel].power.U16_P_Last_OneCycleTime    = hlw8012_detect_channel[channel].power.U16_P_OneCycleTime;
                hlw8012_detect_channel[channel].power.U16_P_Last_CNT             = hlw8012_detect_channel[channel].power.U16_P_CNT;
                hlw8012_detect_channel[channel].power.B_P_Last_TestOneCycle_Mode = hlw8012_detect_channel[channel].power.B_P_TestOneCycle_Mode;
                hlw8012_detect_channel[channel].power.B_P_OVERFLOW               = 0; // 溢出标志位清零
                hlw8012_detect_channel[channel].power.B_P_Last_OVERFLOW          = hlw8012_detect_channel[channel].power.B_P_OVERFLOW;
                // 清状态参数,重新开始测试
                hlw8012_detect_channel[channel].power.B_P_TestOneCycle_Mode = 0; // 初始化为计数脉冲测量模式
                hlw8012_detect_channel[channel].power.U16_P_TotalTimes      = 0; // 完成一次有效的测量，溢出寄存器清零
                hlw8012_detect_channel[channel].power.U16_P_OneCycleTime    = 0; // 清测量寄存器
                hlw8012_detect_channel[channel].power.U16_P_CNT             = 1;
            }
        }
    }

    // 校正模式
    if (hlw8012_detect_channel[channel].WorkMode == D_CAL_START_MODE) {
        // 记录单位时间内的用电量
        hlw8012_detect_channel[channel].energy.U16_E_Pluse_CNT++;
    }

    // 用电量计量，每0.01度电，用电量寄存器增加0.01度
    if (hlw8012_detect_channel[channel].WorkMode == D_NORMAL_MODE) {
        hlw8012_detect_channel[channel].energy.U16_E_Pluse_CNT++;
        if (hlw8012_detect_channel[channel].energy.U16_E_Pluse_CNT == hlw8012_detect_channel[channel].refrence.U16_REF_001_E_Pluse_CNT) {
            hlw8012_detect_channel[channel].energy.U16_E_Pluse_CNT = 0;
            hlw8012_detect_channel[channel].energy.U16_AC_E++;
        }
    }
}

static void update_channel_time(uint8_t channel)
{
    uint16_t temp;
    uint16_t ref_value;

    if (!is_channel_valid(channel)) {
        return;
    }

    // 检查通道是否使能
    if (hlw8012_detect_channel[channel].channel_state != HLW8012_CHANNEL_ENABLED) {
        return;
    }

    // 校正模式
    if (hlw8012_detect_channel[channel].WorkMode == D_CAL_START_MODE) {
        hlw8012_detect_channel[channel].time.U16_Cal_Times++; // 校正时间36S，1000W负载36S时间消耗0.01度电
        if (hlw8012_detect_channel[channel].time.U16_Cal_Times == D_TIME1_CAL_TIME) {
            hlw8012_detect_channel[channel].time.U16_Cal_Times = 0;
            hlw8012_detect_channel[channel].WorkMode           = D_CAL_END_MODE;
            // 校准结束，根据参考校准功率计算比值
            temp = local_cal_data.refer_power;
            if (temp < 1000) {
                ref_value = 1000 / temp;
                ref_value *= hlw8012_detect_channel[channel].energy.U16_E_Pluse_CNT; // 记录36S时间内的脉冲数，此脉冲数表示0.01度用电量
            } else {
                ref_value = 100000 / temp;
                ref_value = (hlw8012_detect_channel[channel].energy.U16_E_Pluse_CNT * ref_value) / 100; // 记录36S时间内的脉冲数，此脉冲数表示0.01度用电量
            }
            hlw8012_detect_channel[channel].energy.U16_E_Pluse_CNT           = 0;
            hlw8012_detect_channel[channel].refrence.U16_REF_001_E_Pluse_CNT = ref_value;
            local_cal_data.cal_eng[channel]                                  = ref_value;
            calibrating_data.calibrate_complete                              = 1;
        }
    }

    // 功率测量
    if (hlw8012_detect_channel[channel].power.U16_P_CNT != 0) {
        hlw8012_detect_channel[channel].power.U16_P_OneCycleTime++;
        hlw8012_detect_channel[channel].power.U16_P_TotalTimes++;
    }
    if (hlw8012_detect_channel[channel].power.U16_P_TotalTimes >= D_TIME1_P_OVERFLOW) {
        hlw8012_detect_channel[channel].power.B_P_OVERFLOW      = 1; // 溢出，
        hlw8012_detect_channel[channel].power.B_P_Last_OVERFLOW = hlw8012_detect_channel[channel].power.B_P_OVERFLOW;
        // 清状态参数,重新开始测试
        hlw8012_detect_channel[channel].power.U16_P_TotalTimes      = 0; // 清溢出寄存器
        hlw8012_detect_channel[channel].power.U16_P_OneCycleTime    = 0;
        hlw8012_detect_channel[channel].power.U16_P_CNT             = 0; // 等待下一次中断开始计数
        hlw8012_detect_channel[channel].power.B_P_TestOneCycle_Mode = 0; // 初始化为计数脉冲测量模式
    } else if (hlw8012_detect_channel[channel].power.U16_P_OneCycleTime == D_TIME1_100MS) {
        if (hlw8012_detect_channel[channel].power.U16_P_CNT < 2) {
            // 100ms内只有一次中断，说明周期>100ms,采用单周期测量模式
            hlw8012_detect_channel[channel].power.B_P_TestOneCycle_Mode = 1;
        } else {
            // 100ms内有2次或以上数量脉冲，说明周期<100ms，采用计数脉冲测量模式
            hlw8012_detect_channel[channel].power.B_P_TestOneCycle_Mode = 0;
        }
    }
}

/*-------------------------------------------- 功率、电压、电流计算 -------------------------------------------*/
/*=====================================================
 * Function : uint16_t get_channel_power(uint8_t channel)
 * Describe : 获取通道功率
 * Input    : channel - 通道号
 * Output   : none
 * Return   : 功率值
 * Record   : 2014/04/14
=====================================================*/
static uint16_t get_channel_power(uint8_t channel)
{
    uint16_t power_value = 0;
    uint32_t a;
    uint32_t b;
    uint32_t u32_P_Period;

    if (!is_channel_valid(channel)) {
        return 0;
    }

    // 检查通道是否使能
    if (hlw8012_detect_channel[channel].channel_state != HLW8012_CHANNEL_ENABLED) {
        return 0;
    }

    if (hlw8012_detect_channel[channel].power.B_P_Last_TestOneCycle_Mode == 1) {
        // 单周期测量模式
        b = hlw8012_detect_channel[channel].power.U16_P_Last_OneCycleTime;
        b *= 1000; // ms转换成us
        u32_P_Period = b;
    } else {
        // 计数脉冲测量模式
        b = hlw8012_detect_channel[channel].power.U16_P_Last_OneCycleTime;
        b *= 1000;
        u32_P_Period = b / (hlw8012_detect_channel[channel].power.U16_P_Last_CNT - 1);
    }

    hlw8012_detect_channel[channel].power.U32_P_CURRENT_PLUSEWIDTH_TIME = u32_P_Period; // 校正时取U32_P_CURRENT_PLUSEWIDTH_TIME参数作为参考值
    a                                                                   = local_cal_data.refer_power * hlw8012_detect_channel[channel].refrence.U32_P_REF_PLUSEWIDTH_TIME;

    // 避免除零错误
    if (hlw8012_detect_channel[channel].power.U32_P_CURRENT_PLUSEWIDTH_TIME == 0) {
        power_value = 0;
    } else {
        power_value = a / hlw8012_detect_channel[channel].power.U32_P_CURRENT_PLUSEWIDTH_TIME;
    }

    if (power_value == 0xffff) { // 计算溢出
        power_value = 0;
    }

    if (hlw8012_detect_channel[channel].power.B_P_Last_OVERFLOW != 0) {
        power_value = 0;
    }

    return power_value;
}

/*=====================================================
 * Function : uint16_t get_channel_energy(uint8_t channel)
 * Describe : 获取通道能量
 * Input    : channel - 通道号
 * Output   : none
 * Return   : 能量值，单位0.01度
 * Record   : 2014/04/14
=====================================================*/
static uint16_t get_channel_energy(uint8_t channel)
{
    if (!is_channel_valid(channel)) {
        return 0;
    }

    // 检查通道是否使能
    if (hlw8012_detect_channel[channel].channel_state != HLW8012_CHANNEL_ENABLED) {
        return 0;
    }

    return hlw8012_detect_channel[channel].energy.U16_AC_E;
}

// 获取中断引脚对应的通道号
static uint8_t get_channel_from_pin(uint16_t gpio_pin)
{
    uint8_t channel = 0xFF; // 无效通道

    for (uint8_t i = 0; i < TOTAL_CHARGE_PORT; i++) {
        // 检查引脚是否使能
        if (!cf_pins[i].enabled) {
            continue;
        }

        // 检查通道是否使能
        if (hlw8012_detect_channel[i].channel_state != HLW8012_CHANNEL_ENABLED) {
            continue;
        }

        if (cf_pins[i].pin == gpio_pin) {
            // 读取引脚状态，检测是否为低电平
            uint8_t pin_state = 0;

            if (cf_pins[i].gpio == GPIOB) {
                pin_state = (GPIOB->IDR & gpio_pin) ? 1 : 0;
            } else if (cf_pins[i].gpio == GPIOC) {
                pin_state = (GPIOC->IDR & gpio_pin) ? 1 : 0;
            }

            // 如果是低电平，表示有中断发生
            if (pin_state == 0) {
                channel = i;
                break;
            }
        }
    }

    return channel;
}

// 定时器中断回调函数
static void timer_irq_callback(void)
{
    // 更新所有使能通道的时间
    for (uint8_t i = 0; i < TOTAL_CHARGE_PORT; i++) {
        // 只更新使能的通道
        if (hlw8012_detect_channel[i].channel_state == HLW8012_CHANNEL_ENABLED) {
            update_channel_time(i);
        }
    }
}

// 外部中断回调函数
static void exti_irq_callback(uint16_t GPIO_Pin)
{
    uint8_t channel = get_channel_from_pin(GPIO_Pin);

    if (channel < TOTAL_CHARGE_PORT) {
        // 处理CF引脚中断（功率脉冲）
        get_cf_pin_interrupt(channel);
    }
}

// 轮询获取所有通道的功率
static void get_power_poll(void)
{
    static uint8_t check_power_delay = 0;
    uint16_t power_value;

    // 间隔10ms取一次功率值
    if (++check_power_delay >= 10) {
        check_power_delay = 0;
        for (uint8_t i = 0; i < TOTAL_CHARGE_PORT; i++) {
            // 只处理使能的通道
            if (hlw8012_detect_channel[i].channel_state == HLW8012_CHANNEL_ENABLED) {
                // 获取端口功率值
                power_value = get_channel_power(i);
                // 更新功率值
                hlw8012_detect_channel[i].power.U16_AC_P = power_value;
            }
        }
    }
}

// 扫描所有CF引脚的状态变化
void scan_cf_pins(void)
{
    for (uint8_t i = 0; i < TOTAL_CHARGE_PORT; i++) {
        // 只扫描使能的引脚和通道
        if (!cf_pins[i].enabled ||
            hlw8012_detect_channel[i].channel_state != HLW8012_CHANNEL_ENABLED) {
            continue;
        }

        // 读取当前引脚状态
        uint8_t current_state = 1; // 默认高电平

        if (cf_pins[i].gpio == GPIOB) {
            current_state = (GPIOB->IDR & cf_pins[i].pin) ? 1 : 0;
        } else if (cf_pins[i].gpio == GPIOC) {
            current_state = (GPIOC->IDR & cf_pins[i].pin) ? 1 : 0;
        }

        cf_pin_states[i].curr_state = current_state;

        // 检测下降沿 (从高电平到低电平)
        if (cf_pin_states[i].prev_state == 1 && cf_pin_states[i].curr_state == 0) {
            // 引脚状态从高到低，触发中断处理
            exti_irq_callback(cf_pins[i].pin);
        }

        // 更新上一次状态
        cf_pin_states[i].prev_state = cf_pin_states[i].curr_state;
    }
}

// 新增的接口函数实现

// 通道有效性检查
uint8_t bsp_hlw8012_is_channel_valid(uint8_t channel)
{
    return is_channel_valid(channel);
}

// 使能通道
uint8_t bsp_hlw8012_enable_channel(uint8_t channel)
{
    if (!is_channel_valid(channel)) {
        return HLW8012_ERROR_INVALID_CH;
    }

    hlw8012_detect_channel[channel].channel_state = HLW8012_CHANNEL_ENABLED;
    hlw8012_detect_channel[channel].error_code = HLW8012_OK;
    cf_pins[channel].enabled = 1;

    return HLW8012_OK;
}

// 禁用通道
uint8_t bsp_hlw8012_disable_channel(uint8_t channel)
{
    if (!is_channel_valid(channel)) {
        return HLW8012_ERROR_INVALID_CH;
    }

    hlw8012_detect_channel[channel].channel_state = HLW8012_CHANNEL_DISABLED;
    cf_pins[channel].enabled = 0;

    return HLW8012_OK;
}

// 获取通道状态
uint8_t bsp_hlw8012_get_channel_state(uint8_t channel)
{
    if (!is_channel_valid(channel)) {
        return HLW8012_CHANNEL_ERROR;
    }

    return hlw8012_detect_channel[channel].channel_state;
}

// 设置引脚配置
uint8_t bsp_hlw8012_set_pin_config(uint8_t channel, GPIO_TypeDef *gpio, uint16_t pin)
{
    if (!is_channel_valid(channel)) {
        return HLW8012_ERROR_INVALID_CH;
    }

    if (gpio == NULL) {
        return HLW8012_ERROR_PARAM;
    }

    cf_pins[channel].gpio = gpio;
    cf_pins[channel].pin = pin;

    return HLW8012_OK;
}

// 初始化所有通道
uint8_t bsp_hlw8012_init_all(uint8_t mode)
{
    uint8_t result = HLW8012_OK;

    // 初始化校准数据
    init_calibration_data();

    // 初始化所有通道
    for (uint8_t i = 0; i < TOTAL_CHARGE_PORT; i++) {
        uint8_t ret = hlw8012_init(i, mode);
        if (ret != HLW8012_OK) {
            result = ret; // 记录错误，但继续初始化其他通道
        }
    }

    // 初始化引脚状态
    for (uint8_t i = 0; i < TOTAL_CHARGE_PORT; i++) {
        cf_pin_states[i].prev_state = 1; // 默认为高电平
        cf_pin_states[i].curr_state = 1;
    }

    hlw8012_driver_initialized = 1;
    return result;
}

// 获取通道功率
uint16_t bsp_hlw8012_get_channel_power(uint8_t channel)
{
    return get_channel_power(channel);
}

// 获取通道能量
uint16_t bsp_hlw8012_get_channel_energy(uint8_t channel)
{
    return get_channel_energy(channel);
}

// 重置通道能量
void bsp_hlw8012_reset_channel_energy(uint8_t channel)
{
    if (!is_channel_valid(channel)) {
        return;
    }

    hlw8012_detect_channel[channel].energy.U16_AC_E = 0;
    hlw8012_detect_channel[channel].energy.U16_E_Pluse_CNT = 0;
}

// 导出的驱动函数集
const t_hlw8012_drv hlw8012_drv = {
    .init               = hlw8012_init,
    .init_all           = bsp_hlw8012_init_all,
    .exti_callback      = exti_irq_callback,
    .timer_callback     = timer_irq_callback,
    .get_power          = get_power_poll,
    .get_energy         = get_channel_energy,
    .enable_channel     = bsp_hlw8012_enable_channel,
    .disable_channel    = bsp_hlw8012_disable_channel,
    .get_channel_state  = bsp_hlw8012_get_channel_state,
    .set_pin_config     = bsp_hlw8012_set_pin_config
};

// 初始化校准数据
void init_calibration_data(void)
{
    // 设置默认校准值，这里的值应该从EEPROM或Flash中加载
    local_cal_data.refer_power = 1000; // 参考功率默认为1000W

    // 初始化每个通道的校准数据
    for (uint8_t i = 0; i < TOTAL_CHARGE_PORT; i++) {
        local_cal_data.cal_freq[i] = 4975; // 默认周期值，4975us
        local_cal_data.cal_eng[i]  = 360;  // 默认0.01度电脉冲数，可根据实际情况调整
    }

    local_cal_data.overflow_flag        = 0;
    calibrating_data.calibrate_complete = 0;
}

// 初始化HLW8012驱动（兼容旧接口）
uint8_t bsp_hlw8012_init(uint8_t channel, uint8_t mode)
{
    if (!is_channel_valid(channel)) {
        return HLW8012_ERROR_INVALID_CH;
    }

    // 如果驱动未初始化，先初始化校准数据
    if (!hlw8012_driver_initialized) {
        init_calibration_data();
        hlw8012_driver_initialized = 1;
    }

    // 初始化指定通道
    return hlw8012_init(channel, mode);
}

// 启动校准模式
void bsp_hlw8012_start_calibration(uint8_t channel)
{
    if (is_channel_valid(channel) &&
        hlw8012_detect_channel[channel].channel_state == HLW8012_CHANNEL_ENABLED) {
        hlw8012_detect_channel[channel].WorkMode               = D_CAL_START_MODE;
        hlw8012_detect_channel[channel].time.U16_Cal_Times     = 0;
        hlw8012_detect_channel[channel].energy.U16_E_Pluse_CNT = 0;
    }
}

// 保存校准数据到flash或EEPROM
void bsp_hlw8012_save_calibration_data(void)
{
    // 此处实现保存校准数据到EEPROM或Flash的代码
    // 根据具体的MCU和存储器实现方式进行编写
}

// 加载校准数据
void bsp_hlw8012_load_calibration_data(void)
{
    // 此处实现从EEPROM或Flash加载校准数据的代码
    // 根据具体的MCU和存储器实现方式进行编写
}

// 读取所有通道的数据
void bsp_hlw8012_read_all_channels(void)
{
    // 顺序扫描所有使能的通道
    for (uint8_t i = 0; i < TOTAL_CHARGE_PORT; i++) {
        // 只读取使能的通道
        if (hlw8012_detect_channel[i].channel_state == HLW8012_CHANNEL_ENABLED) {
            // 读取功率和能量
            uint16_t power  = get_channel_power(i);
            uint16_t energy = get_channel_energy(i);

            // 在此处可以对读取的数据进行处理，例如输出到串口或保存到缓冲区
            // 可以添加数据处理逻辑
        }
    }
}
